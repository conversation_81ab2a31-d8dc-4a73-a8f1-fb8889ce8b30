import os
import json
import asyncio
import logging
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime

from config.config import Config
from .document_processor import process_files_async

logger = logging.getLogger(__name__)


def load_preprocessor_config_original() -> Dict[str, Any]:
    """加载预处理程序配置"""
    config_path = Config.PREPROCESSOR_CONFIG['config_file']

    default_config = {
        "api": {
            "deepseek_api_base": "https://api.deepseek.com/v1",
            "deepseek_api_key": "your_api_key_here",
            "moonshot_api_key": "your_api_key_here"
        },
        "files": [],
        "use_cache": True,
        "verify_ssl": False,
        "manual_analysis": False
    }

    if not os.path.exists(config_path):
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=4)
        return default_config

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 使用递归函数确保配置完整性
        def ensure_config_fields(target, default):
            for key, value in default.items():
                if key not in target:
                    target[key] = value
                elif isinstance(value, dict) and isinstance(target[key], dict):
                    ensure_config_fields(target[key], value)

        # 更新配置，确保所有默认字段存在
        ensure_config_fields(config, default_config)

        # 保存更新后的配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)

        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return default_config


def save_preprocessor_config_original(config: Dict[str, Any]) -> bool:
    """保存配置到config.json"""
    config_path = Config.PREPROCESSOR_CONFIG['config_file']
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        logger.error(f"保存配置失败: {e}")
        return False


def scan_docx_files() -> List[str]:
    """扫描docx目录下的所有.docx文件，排除以.~开头的临时文件"""
    docx_dir = Config.PREPROCESSOR_CONFIG['docx_dir']
    if not os.path.exists(docx_dir):
        os.makedirs(docx_dir)
        return []

    docx_files = [f for f in os.listdir(docx_dir) if f.lower().endswith('.docx') and not f.startswith('.~')]

    if docx_files:
        logger.info(f"在docx目录中找到 {len(docx_files)} 个.docx文件")
    else:
        logger.warning("docx目录中没有找到.docx文件")
        logger.warning("如果有.doc格式文件，请先转换为.docx格式")

    return docx_files


def update_config_with_docx_files(config: Dict[str, Any]) -> Dict[str, Any]:
    """扫描docx目录中的docx文件并更新配置"""
    docx_files = scan_docx_files()

    if docx_files:
        # 更新files字段
        config["files"] = docx_files
        save_preprocessor_config_original(config)

    return config

class PreprocessorManager:
    """预处理程序管理器"""
    
    def __init__(self, socketio=None):
        self.socketio = socketio
        self.is_processing = False
        self.current_progress = 0
        
    def emit_log(self, message: str, level: str = 'info'):
        """发送日志消息"""
        if self.socketio:
            self.socketio.emit('preprocessor_log', {
                'message': message,
                'level': level,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        logger.log(getattr(logging, level.upper(), logging.INFO), message)
    
    def emit_progress(self, percentage: float, task: str):
        """发送进度更新"""
        self.current_progress = percentage
        print(f"[DEBUG] 发送进度更新: {percentage}% - {task}")  # 添加调试信息
        if self.socketio:
            self.socketio.emit('preprocessor_progress', {
                'percentage': percentage,
                'task': task
            })
            print(f"[DEBUG] Socket.IO事件已发送")  # 添加调试信息
        else:
            print(f"[DEBUG] Socket.IO实例为空！")
    
    def get_config(self) -> Dict[str, Any]:
        """获取预处理程序配置"""
        try:
            config = load_preprocessor_config_original()
            # 自动扫描并更新文件列表
            config = update_config_with_docx_files(config)
            return config
        except Exception as e:
            self.emit_log(f"获取配置失败: {e}", 'error')
            return {
                "api": {
                    "deepseek_api_base": "https://api.deepseek.com/v1",
                    "deepseek_api_key": "your_api_key_here",
                    "moonshot_api_key": "your_api_key_here"
                },
                "files": [],
                "use_cache": True,
                "verify_ssl": False,
                "manual_analysis": False
            }
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存预处理程序配置"""
        try:
            save_preprocessor_config_original(config)
            self.emit_log("配置保存成功", 'info')
            return True
        except Exception as e:
            self.emit_log(f"配置保存失败: {e}", 'error')
            return False
    
    def scan_files(self) -> List[str]:
        """扫描docx目录中的文件"""
        try:
            files = scan_docx_files()
            self.emit_log(f"扫描到 {len(files)} 个文件", 'info')
            return files
        except Exception as e:
            self.emit_log(f"文件扫描失败: {e}", 'error')
            return []
    
    def remove_file_from_config(self, filename: str) -> bool:
        """从配置中移除文件"""
        try:
            config = self.get_config()
            if filename in config.get('files', []):
                config['files'].remove(filename)
                self.save_config(config)
                self.emit_log(f"已从配置中移除文件: {filename}", 'info')
                return True
            else:
                self.emit_log(f"文件不在配置列表中: {filename}", 'warning')
                return False
        except Exception as e:
            self.emit_log(f"移除文件失败: {e}", 'error')
            return False

    async def process_documents(self) -> bool:
        """异步处理文档"""
        if self.is_processing:
            self.emit_log("处理正在进行中，请等待完成", 'warning')
            return False

        try:
            self.is_processing = True
            self.emit_log("开始文档预处理", 'info')
            self.emit_progress(10, "加载配置...")
            
            # 获取配置
            config = self.get_config()
            files = config.get('files', [])
            
            if not files:
                self.emit_log("没有待处理的文件", 'warning')
                self.is_processing = False
                return False
            
            self.emit_progress(20, f"准备处理 {len(files)} 个文件...")
            
            # 检查API密钥 - 题目处理阶段需要
            api_key = config.get('api', {}).get('deepseek_api_key', '')
            if api_key == 'your_api_key_here' or not api_key:
                self.emit_log("警告: API密钥未配置，题目处理阶段将失败", 'warning')
            
            self.emit_progress(30, "开始处理文档...")

            # 处理文件 - 使用新的集成功能
            docx_dir = Config.PREPROCESSOR_CONFIG['docx_dir']
            work_dir = Config.PREPROCESSOR_CONFIG['work_dir']  # 使用geo_question_web内部工作目录

            # 确保工作目录存在
            os.makedirs(work_dir, exist_ok=True)
            # 注意：不再创建全局目录，子目录由get_doc_directories按需创建

            try:
                processed_files = await process_files_async(files, docx_dir, work_dir, config)

                # 检查处理结果和缓存文件状态
                from .document_processor import get_doc_directories

                # 检查是否有缓存文件存在但为空模板（表示需要手动分析）
                for file in files:
                    doc_name = os.path.splitext(file)[0]
                    directories = get_doc_directories(doc_name, work_dir)
                    cache_file = os.path.join(directories["cache_dir"], f"{doc_name}_analysis_cache.json")

                    if os.path.exists(cache_file):
                        try:
                            with open(cache_file, 'r', encoding='utf-8') as f:
                                cache_data = json.load(f)
                                # 检查是否为空模板 - 题组信息必须存在且不为空
                                题组信息 = cache_data.get("题组信息", [])
                                if not 题组信息 or len(题组信息) == 0:
                                    from .prompts import get_document_analysis_prompt

                                    self.emit_log("检测到需要手动分析的文档，请提交分析数据", 'warning')
                                    self.emit_log(f"准备发送show_json_submit事件，文档名: {doc_name}", 'info')

                                    # 发送进度更新，显示等待手动分析
                                    self.emit_progress(50, '等待手动分析文档结构...')

                                    # 发送特殊事件到前端，触发JSON提交界面
                                    if self.socketio:
                                        try:
                                            self.socketio.emit('show_json_submit', {
                                                'document_name': doc_name,
                                                'prompt': get_document_analysis_prompt()
                                            })
                                            self.emit_log("show_json_submit事件已发送", 'info')
                                        except Exception as e:
                                            self.emit_log(f"发送show_json_submit事件失败: {e}", 'error')
                                    else:
                                        self.emit_log("socketio对象为空，无法发送事件", 'error')

                                    # 注意：不要设置 is_processing = False，保持处理状态
                                    return 'manual_analysis_required'
                        except Exception:
                            pass

                # 如果没有成功处理的文件
                if not processed_files:
                    self.emit_log("没有成功处理的文件", 'warning')
            except Exception as e:
                # 检查异常类型
                from .document_processor import ManualAnalysisRequiredException, CacheFoundException

                if isinstance(e, CacheFoundException):
                    self.emit_log(f"发现文档 {e.document_name} 的缓存文件", 'info')

                    # 发送缓存选择事件到前端
                    if self.socketio:
                        # 生成缓存摘要信息
                        cache_summary = self._generate_cache_summary(e.cache_data)
                        self.socketio.emit('show_cache_choice', {
                            'document_name': e.document_name,
                            'cache_file': e.cache_file,
                            'cache_summary': cache_summary
                        })

                    # 暂停处理，等待用户选择
                    self.is_processing = False
                    return 'cache_choice_required'

                elif isinstance(e, ManualAnalysisRequiredException):
                    self.emit_log("检测到需要手动分析的文档，请提交分析数据", 'warning')

                    # 发送特殊事件到前端，触发JSON提交界面
                    if self.socketio:
                        self.socketio.emit('show_json_submit', {
                            'document_name': e.document_name,
                            'prompt': e.prompt
                        })

                    # 暂停处理，等待用户提交分析数据
                    self.is_processing = False
                    return 'manual_analysis_required'
                else:
                    raise e

            # 更新进度
            total_files = len(files)
            processed_count = len(processed_files)

            self.emit_progress(90, f"处理完成 {processed_count}/{total_files} 个文件")

            # 输出结果
            if processed_files:
                self.emit_log(f"成功处理 {processed_count}/{total_files} 个文件", 'info')
                for file_name, output_path, json_path in processed_files:
                    self.emit_log(f"[OK] {file_name} -> {output_path}", 'info')
                    if json_path:
                        self.emit_log(f"  JSON: {json_path}", 'info')
            else:
                self.emit_log("没有成功处理的文件", 'warning')

            self.emit_progress(100, "处理完成")

            if self.socketio:
                self.socketio.emit('preprocessor_complete', {
                    'processed_count': processed_count,
                    'total_count': total_files,
                    'processed_files': processed_files
                })
            
            return True
            
        except Exception as e:
            self.emit_log(f"处理过程中发生错误: {e}", 'error')
            return False
        finally:
            self.is_processing = False
    
    def get_processing_status(self) -> Dict[str, Any]:
        """获取处理状态"""
        return {
            'is_processing': self.is_processing,
            'progress': self.current_progress
        }
    
    def upload_file(self, file_data: bytes, filename: str, overwrite: bool = True) -> bool:
        """上传文件到docx目录"""
        try:
            if not filename.endswith('.docx'):
                self.emit_log(f"不支持的文件格式: {filename}", 'error')
                return False

            docx_dir = Config.PREPROCESSOR_CONFIG['docx_dir']
            file_path = os.path.join(docx_dir, filename)

            # 检查文件是否已存在
            if os.path.exists(file_path):
                if overwrite:
                    self.emit_log(f"文件已存在，将覆盖: {filename}", 'warning')
                else:
                    self.emit_log(f"文件已存在，跳过上传: {filename}", 'warning')
                    return False

            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(file_data)

            self.emit_log(f"文件上传成功: {filename}", 'info')

            # 更新配置文件
            config = self.get_config()
            if filename not in config.get('files', []):
                config.setdefault('files', []).append(filename)
                self.save_config(config)

            return True

        except Exception as e:
            self.emit_log(f"文件上传失败: {e}", 'error')
            return False
    
    def get_file_info(self) -> List[Dict[str, Any]]:
        """获取文件信息"""
        try:
            config = self.get_config()
            files = config.get('files', [])
            docx_dir = Config.PREPROCESSOR_CONFIG['docx_dir']
            
            file_info = []
            for filename in files:
                file_path = os.path.join(docx_dir, filename)
                info = {
                    'filename': filename,
                    'exists': os.path.exists(file_path),
                    'size': 0,
                    'modified': None
                }
                
                if info['exists']:
                    stat = os.stat(file_path)
                    info['size'] = stat.st_size
                    info['modified'] = datetime.fromtimestamp(stat.st_mtime)
                
                file_info.append(info)
            
            return file_info

        except Exception as e:
            self.emit_log(f"获取文件信息失败: {e}", 'error')
            return []

    def _generate_cache_summary(self, cache_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成缓存文件的摘要信息"""
        try:
            题组信息 = cache_data.get("题组信息", [])
            题型统计 = cache_data.get("题型统计", {})
            总题目数 = cache_data.get("总题目数", 0)

            return {
                "题组数量": len(题组信息),
                "总题目数": 总题目数,
                "题型统计": 题型统计,
                "题组范围": f"{题组信息[0]['起始题号']}-{题组信息[-1]['结束题号']}" if 题组信息 else "无",
                "创建时间": "未知"  # 可以从文件修改时间获取
            }
        except Exception:
            return {"错误": "无法解析缓存文件"}

    def save_analysis_data(self, document_name: str, analysis_data: Dict[str, Any]) -> bool:
        """保存分析数据到缓存文件"""
        try:
            from .document_processor import get_doc_directories

            # 使用与document_processor相同的目录结构
            work_dir = Config.PREPROCESSOR_CONFIG['work_dir']
            directories = get_doc_directories(document_name, work_dir)
            cache_file = os.path.join(directories["cache_dir"], f"{document_name}_analysis_cache.json")

            # 保存分析数据
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, ensure_ascii=False, indent=2)

            self.emit_log(f"分析数据已保存: {cache_file}", 'info')
            return True

        except Exception as e:
            self.emit_log(f"保存分析数据失败: {e}", 'error')
            return False

    def continue_processing_with_cache(self, document_name: str) -> bool:
        """使用缓存继续处理"""
        try:
            self.is_processing = True
            self.emit_log("使用缓存数据继续处理...", 'info')

            # 创建异步任务来处理
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(self.continue_processing(document_name))
                return result
            finally:
                loop.close()

        except Exception as e:
            self.emit_log(f"使用缓存处理失败: {e}", 'error')
            self.is_processing = False
            return False

    def restart_analysis(self, document_name: str) -> str:
        """重新开始分析，清除缓存"""
        try:
            from .document_processor import get_doc_directories

            # 获取缓存文件路径
            work_dir = Config.PREPROCESSOR_CONFIG['work_dir']
            directories = get_doc_directories(document_name, work_dir)
            cache_file = os.path.join(directories["cache_dir"], f"{document_name}_analysis_cache.json")

            # 清除缓存文件内容，重置为空模板
            empty_template = {
                "题组信息": [],
                "图片分析": {
                    "共享材料图片": [],
                    "题目图片": {},
                    "选项图片": {},
                    "未分类图片": []
                },
                "图片目录": directories["images_dir"],
                "表格信息": []
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(empty_template, f, ensure_ascii=False, indent=2)

            self.emit_log("缓存已清除，开始重新分析", 'info')

            # 触发手动分析流程
            from .prompts import get_document_analysis_prompt
            if self.socketio:
                self.socketio.emit('show_json_submit', {
                    'document_name': document_name,
                    'prompt': get_document_analysis_prompt()
                })

            return 'manual_analysis_required'

        except Exception as e:
            self.emit_log(f"重新分析失败: {e}", 'error')
            return False

    def get_cache_data(self, document_name: str) -> Dict[str, Any]:
        """获取缓存数据"""
        try:
            from .document_processor import get_doc_directories

            work_dir = Config.PREPROCESSOR_CONFIG['work_dir']
            directories = get_doc_directories(document_name, work_dir)
            cache_file = os.path.join(directories["cache_dir"], f"{document_name}_analysis_cache.json")

            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {}

        except Exception as e:
            self.emit_log(f"获取缓存数据失败: {e}", 'error')
            return {}

    async def continue_processing(self, document_name: str) -> bool:
        """继续处理指定文档"""
        try:
            from .document_processor import process_file_async

            # 构建文档路径
            docx_dir = Config.PREPROCESSOR_CONFIG['docx_dir']
            work_dir = Config.PREPROCESSOR_CONFIG['work_dir']
            docx_path = os.path.join(docx_dir, f"{document_name}.docx")

            if not os.path.exists(docx_path):
                self.emit_log(f"文档文件不存在: {docx_path}", 'error')
                return False

            # 获取配置
            config = self.get_config()

            self.emit_log(f"开始处理文档: {document_name}", 'info')

            # 处理文档，跳过缓存检查（因为用户已经选择了如何处理缓存）
            def progress_callback(percentage, task):
                self.emit_progress(percentage, task)

            # 发送开始处理的进度更新
            self.emit_progress(60, f'开始处理文档: {document_name}...')

            output_path, json_path = await process_file_async(docx_path, work_dir, config, skip_cache_check=True, progress_callback=progress_callback)

            self.emit_log(f"文档处理完成: {output_path}", 'info')
            if json_path:
                self.emit_log(f"JSON文件生成: {json_path}", 'info')

            # 发送完成进度更新
            self.emit_progress(100, '文档处理完成')

            # 发送完成事件
            if self.socketio:
                self.socketio.emit('preprocessor_complete', {
                    'processed_count': 1,
                    'total_count': 1,
                    'processed_files': [(document_name, output_path, json_path)]
                })

            return True

        except Exception as e:
            self.emit_log(f"继续处理失败: {e}", 'error')
            return False
        finally:
            # 重置处理状态
            self.is_processing = False
