# 地理题库系统部署指南

## 🚀 快速启动

开发环境：
python3 scripts/start.py --env development --debug

生产环境：
python start.py --env production

# 在宝塔面板的启动命令中填写：
python start.py --env production

高性能部署：
gunicorn --config gunicorn_config.py wsgi:application


### 1. 环境准备
```bash
# 确保Python 3.7+已安装
python --version

# 安装项目依赖
pip install -r requirements.txt
```

### 2. 数据库配置
```bash
# 设置数据库密码（生产环境必须）
export DB_PASSWORD=your_database_password
```

### 3. 启动应用

#### 开发环境
```bash
python start.py --env development
```

#### 生产环境
```bash
python start.py --env production
```

#### 自定义端口
```bash
python start.py --port 8080
```

#### 后台运行（Linux服务器）
```bash
# 使用nohup后台运行
nohup python start.py --env production > app.log 2>&1 &

# 查看运行状态
ps aux | grep python

# 停止服务
pkill -f start.py
```

## 🌐 宝塔Linux部署

### 1. 宝塔面板配置
- **项目名称**: geo_question_web
- **Python版本**: 3.7+
- **启动方式**: 命令行启动
- **启动命令**: `python start.py --env production`
- **运行用户**: www

### 2. 环境变量设置
在宝塔面板的**环境变量**中添加：
```bash
DB_PASSWORD=your_database_password
```

### 3. Gunicorn高性能部署（可选）
```bash
# 直接使用gunicorn命令
gunicorn --config gunicorn_config.py wsgi:application
```

### 3. 配置Nginx（可选，用于反向代理）

创建Nginx配置文件 `/etc/nginx/sites-available/geo_questions`:

```nginx
server {
    listen 80;
    server_name your_domain.com;  # 替换为你的域名

    location / {
        proxy_pass http://127.0.0.1:5002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket支持
    location /socket.io/ {
        proxy_pass http://127.0.0.1:5002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件
    location /static/ {
        alias /path/to/your/project/static/;
        expires 30d;
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/geo_questions /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔧 系统服务配置（Linux）

### 创建systemd服务文件

创建 `/etc/systemd/system/geo-questions.service`:

```ini
[Unit]
Description=Geography Questions Web Application
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/project/geo_question_web
Environment=FLASK_ENV=production
Environment=DB_PASSWORD=your_database_password
ExecStart=/usr/bin/python3 start_gunicorn.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启用服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable geo-questions
sudo systemctl start geo-questions
sudo systemctl status geo-questions
```

## 📋 部署检查清单

### 部署前检查
- [ ] Python 3.7+ 已安装
- [ ] 所有依赖包已安装 (`pip install -r requirements.txt`)
- [ ] 数据库服务正常运行
- [ ] 数据库密码已设置环境变量
- [ ] 防火墙已开放5002端口

### 部署后检查
- [ ] 应用正常启动，无错误日志
- [ ] 数据库连接正常
- [ ] 系统首页可以正常访问 (http://localhost:5002/)
- [ ] 管理后台可以正常访问 (http://localhost:5002/admin)
- [ ] 刷题系统可以正常访问 (http://localhost:5002/quiz/home)
- [ ] WebSocket功能正常（实时日志显示）
- [ ] 文件上传功能正常

## 🛠️ 常见问题解决

### 1. 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep 5002

# 杀死占用进程
sudo kill -9 <PID>
```

### 2. 数据库连接失败
- 检查数据库服务是否启动
- 确认数据库密码环境变量是否设置
- 验证网络连接

### 3. 权限问题（Linux）
```bash
# 给予执行权限
chmod +x run_prod.py
chmod +x start_gunicorn.py

# 检查文件所有者
chown -R www-data:www-data /path/to/project
```

### 4. 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看Gunicorn日志
tail -f logs/error.log
tail -f logs/access.log
```

## 🔒 安全建议

1. **数据库安全**
   - 使用强密码
   - 限制数据库访问IP
   - 定期备份数据

2. **应用安全**
   - 设置强SECRET_KEY
   - 使用HTTPS（配置SSL证书）
   - 定期更新依赖包

3. **服务器安全**
   - 配置防火墙
   - 定期更新系统
   - 使用非root用户运行应用

## 📞 技术支持

如果遇到部署问题，可以：
1. 查看日志文件定位问题
2. 检查网络和防火墙配置
3. 确认所有依赖都已正确安装
