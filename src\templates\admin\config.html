{% extends "base.html" %}

{% block title %}系统配置 - 地理题库管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-cog"></i> 系统配置</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-success" onclick="saveAllConfigs()">
            <i class="fas fa-save"></i> 保存所有配置
        </button>
    </div>
</div>

<!-- 配置导航 -->
<ul class="nav nav-tabs mb-4" id="configTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="preprocessor-tab" data-bs-toggle="tab" data-bs-target="#preprocessor-config" type="button" role="tab">
            <i class="fas fa-file-word"></i> 预处理配置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database-config" type="button" role="tab">
            <i class="fas fa-database"></i> 数据库配置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="pusher-tab" data-bs-toggle="tab" data-bs-target="#pusher-config" type="button" role="tab">
            <i class="fas fa-paper-plane"></i> 推送配置
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system-config" type="button" role="tab">
            <i class="fas fa-server"></i> 系统配置
        </button>
    </li>
</ul>

<div class="tab-content" id="configTabContent">
    <!-- 预处理配置 -->
    <div class="tab-pane fade show active" id="preprocessor-config" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-file-word"></i> 预处理程序配置
            </div>
            <div class="card-body">
                <form id="preprocessor-form">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>API配置</h6>
                            <div class="mb-3">
                                <label for="deepseek-api-key" class="form-label">DeepSeek API密钥</label>
                                <input type="password" class="form-control" id="deepseek-api-key" 
                                       value="{{ preprocessor_config.api.deepseek_api_key }}" 
                                       placeholder="输入DeepSeek API密钥">
                                <div class="form-text">用于AI分析文档内容</div>
                            </div>
                            <div class="mb-3">
                                <label for="deepseek-api-base" class="form-label">DeepSeek API地址</label>
                                <input type="url" class="form-control" id="deepseek-api-base" 
                                       value="{{ preprocessor_config.api.deepseek_api_base }}" 
                                       placeholder="https://api.deepseek.com/v1">
                            </div>
                            <div class="mb-3">
                                <label for="moonshot-api-key" class="form-label">Moonshot API密钥 (备用)</label>
                                <input type="password" class="form-control" id="moonshot-api-key" 
                                       value="{{ preprocessor_config.api.moonshot_api_key }}" 
                                       placeholder="输入Moonshot API密钥">
                                <div class="form-text">备用AI服务</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>处理选项</h6>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use-cache" 
                                           {% if preprocessor_config.use_cache %}checked{% endif %}>
                                    <label class="form-check-label" for="use-cache">
                                        启用缓存
                                    </label>
                                    <div class="form-text">缓存分析结果以避免重复处理</div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="verify-ssl" 
                                           {% if preprocessor_config.verify_ssl %}checked{% endif %}>
                                    <label class="form-check-label" for="verify-ssl">
                                        验证SSL证书
                                    </label>
                                    <div class="form-text">API请求时验证SSL证书</div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="manual-analysis" 
                                           {% if preprocessor_config.manual_analysis %}checked{% endif %}>
                                    <label class="form-check-label" for="manual-analysis">
                                        手动分析模式
                                    </label>
                                    <div class="form-text">需要手动输入分析结果</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="button" class="btn btn-primary" onclick="savePreprocessorConfig()">
                            <i class="fas fa-save"></i> 保存预处理配置
                        </button>
                        <button type="button" class="btn btn-outline-secondary ms-2" onclick="testApiConnection()">
                            <i class="fas fa-plug"></i> 测试API连接
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 数据库配置 -->
    <div class="tab-pane fade" id="database-config" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-database"></i> 数据库配置
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    数据库配置当前在代码中设置，如需修改请联系管理员。
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6>连接信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>主机:</strong></td>
                                <td>47.122.50.189</td>
                            </tr>
                            <tr>
                                <td><strong>端口:</strong></td>
                                <td>3306</td>
                            </tr>
                            <tr>
                                <td><strong>数据库:</strong></td>
                                <td>geo_questions</td>
                            </tr>
                            <tr>
                                <td><strong>用户名:</strong></td>
                                <td>Geo_Questions</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>连接状态</h6>
                        <div id="db-status" class="mb-3">
                            <span class="badge bg-secondary">检测中...</span>
                        </div>
                        <button type="button" class="btn btn-outline-primary" onclick="testDatabaseConnection()">
                            <i class="fas fa-plug"></i> 测试数据库连接
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 推送配置 -->
    <div class="tab-pane fade" id="pusher-config" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-paper-plane"></i> 推送配置
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>PushPlus配置</h6>
                        <div class="mb-3">
                            <label for="pushplus-token" class="form-label">PushPlus Token</label>
                            <input type="password" class="form-control" id="pushplus-token" 
                                   value="{{ pusher_config.pushplus_token }}" 
                                   placeholder="输入PushPlus推送Token">
                            <div class="form-text">用于发送推送通知</div>
                        </div>
                        <div class="mb-3">
                            <label for="image-url-base" class="form-label">图片URL基础地址</label>
                            <input type="url" class="form-control" id="image-url-base" 
                                   value="{{ pusher_config.image_url_base }}" 
                                   placeholder="http://example.com/images/">
                            <div class="form-text">题目图片的基础URL</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>推送状态</h6>
                        <div class="mb-3">
                            <label class="form-label">模板状态</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i> 交互式HTML模板已集成
                                </span>
                            </div>
                            <div class="form-text">推送模板已内置到系统中</div>
                        </div>
                        <div class="mb-3">
                            <a href="/pusher" class="btn btn-outline-info">
                                <i class="fas fa-paper-plane"></i> 前往推送管理
                            </a>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        推送配置现在可以在<strong>推送管理页面</strong>中进行动态配置，无需重启服务器。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统配置 -->
    <div class="tab-pane fade" id="system-config" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-server"></i> 系统配置
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>日志配置</h6>
                        <div class="mb-3">
                            <label for="log-level" class="form-label">日志级别</label>
                            <select class="form-select" id="log-level">
                                <option value="DEBUG">DEBUG</option>
                                <option value="INFO" selected>INFO</option>
                                <option value="WARNING">WARNING</option>
                                <option value="ERROR">ERROR</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable-file-log" checked>
                                <label class="form-check-label" for="enable-file-log">
                                    启用文件日志
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>性能配置</h6>
                        <div class="mb-3">
                            <label for="max-workers" class="form-label">最大工作线程</label>
                            <input type="number" class="form-control" id="max-workers" value="4" min="1" max="16">
                        </div>
                        <div class="mb-3">
                            <label for="request-timeout" class="form-label">请求超时时间 (秒)</label>
                            <input type="number" class="form-control" id="request-timeout" value="30" min="5" max="300">
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="saveSystemConfig()">
                        <i class="fas fa-save"></i> 保存系统配置
                    </button>
                    <button type="button" class="btn btn-outline-warning ms-2" onclick="restartSystem()">
                        <i class="fas fa-redo"></i> 重启系统
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置日志 -->
<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-terminal"></i> 配置日志
        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearConfigLogs()">
            <i class="fas fa-trash"></i> 清空
        </button>
    </div>
    <div class="card-body">
        <div id="config-log-container" class="log-container">
            <div class="log-entry log-info">
                <span class="log-timestamp">[系统启动]</span>
                配置管理模块已就绪，等待操作...
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function savePreprocessorConfig() {
    const config = {
        api: {
            deepseek_api_key: document.getElementById('deepseek-api-key').value,
            deepseek_api_base: document.getElementById('deepseek-api-base').value,
            moonshot_api_key: document.getElementById('moonshot-api-key').value
        },
        use_cache: document.getElementById('use-cache').checked,
        verify_ssl: document.getElementById('verify-ssl').checked,
        manual_analysis: document.getElementById('manual-analysis').checked,
        files: {{ preprocessor_config.files | tojson }}
    };
    
    addConfigLog('保存预处理配置...', 'info');
    
    fetch('/api/config/preprocessor', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addConfigLog('预处理配置保存成功', 'info');
        } else {
            addConfigLog(`保存失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addConfigLog(`保存请求失败: ${error}`, 'error');
    });
}

function testApiConnection() {
    const apiKey = document.getElementById('deepseek-api-key').value;
    const apiBase = document.getElementById('deepseek-api-base').value;
    
    if (!apiKey || apiKey === 'your_api_key_here') {
        addConfigLog('请先输入有效的API密钥', 'warning');
        return;
    }
    
    addConfigLog('测试API连接...', 'info');
    // 这里应该实现API连接测试
    setTimeout(() => {
        addConfigLog('API连接测试功能待实现', 'warning');
    }, 1000);
}

function testDatabaseConnection() {
    addConfigLog('测试数据库连接...', 'info');
    
    fetch('/api/test-connection')
        .then(response => response.json())
        .then(data => {
            const statusElement = document.getElementById('db-status');
            if (data.success) {
                statusElement.innerHTML = '<span class="badge bg-success">连接正常</span>';
                addConfigLog('数据库连接测试成功', 'info');
            } else {
                statusElement.innerHTML = '<span class="badge bg-danger">连接失败</span>';
                addConfigLog(`数据库连接失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            document.getElementById('db-status').innerHTML = '<span class="badge bg-warning">测试失败</span>';
            addConfigLog(`连接测试请求失败: ${error}`, 'error');
        });
}

// 推送配置相关功能已移至推送管理页面

function previewTemplate() {
    addConfigLog('模板预览功能待实现', 'warning');
}

function saveSystemConfig() {
    addConfigLog('系统配置保存功能待实现', 'warning');
}

function restartSystem() {
    if (confirm('确定要重启系统吗？这将中断所有正在进行的操作。')) {
        addConfigLog('系统重启功能待实现', 'warning');
    }
}

function saveAllConfigs() {
    addConfigLog('开始保存所有配置...', 'info');
    savePreprocessorConfig();
    // 其他配置保存...
}

function addConfigLog(message, level) {
    const logContainer = document.getElementById('config-log-container');
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${level}`;
    logEntry.innerHTML = `<span class="log-timestamp">[${new Date().toLocaleString()}]</span>${message}`;
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

function clearConfigLogs() {
    const logContainer = document.getElementById('config-log-container');
    logContainer.innerHTML = '<div class="log-entry log-info"><span class="log-timestamp">[' + 
        new Date().toLocaleString() + ']</span>日志已清空</div>';
}

// 页面加载时测试数据库连接
document.addEventListener('DOMContentLoaded', function() {
    testDatabaseConnection();
});
</script>
{% endblock %}
