import os
import sys
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import pymysql

# 尝试从同一目录导入tpdb模块
try:
    from .tpdb import get_or_create_paper, insert_questions_to_db
except ImportError as e:
    logging.error(f"无法导入上传程序模块: {e}")
    # 提供备用函数
    def get_or_create_paper(connection, paper_name, source_file, image_folder):
        return None
    def insert_questions_to_db(connection, question_list, paper_id):
        pass

from config.config import Config

logger = logging.getLogger(__name__)

class UploaderManager:
    """数据库上传管理器"""
    
    def __init__(self, socketio=None):
        self.socketio = socketio
        self.is_uploading = False
        self.current_progress = 0
        
    def emit_log(self, message: str, level: str = 'info'):
        """发送日志消息"""
        if self.socketio:
            self.socketio.emit('uploader_log', {
                'message': message,
                'level': level,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        logger.log(getattr(logging, level.upper(), logging.INFO), message)
    
    def emit_progress(self, percentage: float, task: str, processed: int = 0, total: int = 0):
        """发送进度更新"""
        self.current_progress = percentage
        if self.socketio:
            self.socketio.emit('uploader_progress', {
                'percentage': percentage,
                'task': task,
                'processed': processed,
                'total': total
            })
    
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(**Config.DB_CONFIG)
            return connection
        except Exception as e:
            self.emit_log(f"数据库连接失败: {e}", 'error')
            return None
    
    def validate_json_data(self, data: List[Dict[str, Any]]) -> bool:
        """验证JSON数据格式"""
        if not isinstance(data, list) or len(data) == 0:
            self.emit_log("数据格式错误：应为非空数组", 'error')
            return False
        
        required_fields = ['id', 'source', 'question_text']
        for i, item in enumerate(data):
            if not isinstance(item, dict):
                self.emit_log(f"第{i+1}项数据格式错误：应为对象", 'error')
                return False
            
            for field in required_fields:
                if field not in item:
                    self.emit_log(f"第{i+1}项缺少必需字段：{field}", 'error')
                    return False
        
        self.emit_log(f"数据验证通过，共{len(data)}条记录", 'info')
        return True
    
    def upload_to_database(self, json_data: List[Dict[str, Any]]) -> bool:
        """上传数据到数据库"""
        if self.is_uploading:
            self.emit_log("上传正在进行中，请等待完成", 'warning')
            return False
        
        if not self.validate_json_data(json_data):
            return False
        
        connection = self.get_db_connection()
        if not connection:
            return False
        
        try:
            self.is_uploading = True
            self.emit_log("开始上传数据到数据库", 'info')
            self.emit_progress(10, "连接数据库...", 0, len(json_data))
            
            # 获取试卷信息
            paper_name = json_data[0].get('source', '未知试卷')
            self.emit_log(f"试卷名称: {paper_name}", 'info')
            
            # 生成图片文件夹名称
            image_folder_name = self.generate_image_folder_name(paper_name)
            self.emit_progress(20, "创建或获取试卷记录...", 0, len(json_data))
            
            # 获取或创建试卷ID
            paper_id = get_or_create_paper(
                connection, 
                paper_name, 
                f"web_upload_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                image_folder_name
            )
            
            if not paper_id:
                self.emit_log("创建试卷记录失败", 'error')
                return False
            
            self.emit_log(f"试卷ID: {paper_id}", 'info')
            self.emit_progress(30, "开始插入题目数据...", 0, len(json_data))
            
            # 批量插入题目数据
            batch_size = 10  # 每批处理10题
            total_questions = len(json_data)
            processed_count = 0
            
            for i in range(0, total_questions, batch_size):
                batch = json_data[i:i + batch_size]
                
                try:
                    insert_questions_to_db(connection, batch, paper_id)
                    processed_count += len(batch)
                    
                    progress = 30 + (processed_count / total_questions) * 60
                    self.emit_progress(
                        progress, 
                        f"正在插入题目 {processed_count}/{total_questions}...",
                        processed_count,
                        total_questions
                    )
                    
                    self.emit_log(f"已处理 {processed_count}/{total_questions} 题", 'info')
                    
                except Exception as e:
                    self.emit_log(f"插入第{i+1}-{i+len(batch)}题时出错: {e}", 'error')
                    # 继续处理下一批
                    continue
            
            # 提交事务
            connection.commit()
            self.emit_progress(95, "提交数据库事务...", processed_count, total_questions)
            self.emit_log("数据库事务已提交", 'info')
            
            self.emit_progress(100, "上传完成", processed_count, total_questions)
            self.emit_log(f"成功上传 {processed_count}/{total_questions} 题到数据库", 'info')
            
            if self.socketio:
                self.socketio.emit('uploader_complete', {
                    'processed': processed_count,
                    'total': total_questions,
                    'paper_id': paper_id,
                    'paper_name': paper_name
                })
            
            return True
            
        except Exception as e:
            self.emit_log(f"上传过程中发生错误: {e}", 'error')
            if connection:
                connection.rollback()
                self.emit_log("已回滚数据库事务", 'warning')
            return False
        finally:
            self.is_uploading = False
            if connection:
                connection.close()
    
    def generate_image_folder_name(self, paper_name: str) -> str:
        """根据试卷名称生成图片文件夹名称"""
        # 简单的名称转换逻辑
        folder_name = paper_name.replace('年', '_').replace('省', '_').replace('高考', '_')
        folder_name = folder_name.replace('真题', '').replace('（', '_').replace('）', '')
        folder_name = folder_name.replace('(', '_').replace(')', '').replace(' ', '_')
        folder_name = folder_name.replace('月', '').lower()
        
        # 移除多余的下划线
        while '__' in folder_name:
            folder_name = folder_name.replace('__', '_')
        folder_name = folder_name.strip('_')
        
        return folder_name or 'unknown_paper'
    
    def get_processed_files(self) -> List[str]:
        """获取已处理的JSON文件列表"""
        try:
            output_dir = Config.PREPROCESSOR_CONFIG['output_dir']
            if not os.path.exists(output_dir):
                return []
            
            json_files = []
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    if file.endswith('.json'):
                        # 获取相对路径
                        rel_path = os.path.relpath(os.path.join(root, file), output_dir)
                        json_files.append(rel_path)
            
            self.emit_log(f"找到 {len(json_files)} 个JSON文件", 'info')
            return json_files
            
        except Exception as e:
            self.emit_log(f"获取文件列表失败: {e}", 'error')
            return []
    
    def load_processed_file(self, filename: str) -> Optional[List[Dict[str, Any]]]:
        """加载已处理的JSON文件"""
        try:
            output_dir = Config.PREPROCESSOR_CONFIG['output_dir']
            file_path = os.path.join(output_dir, filename)
            
            if not os.path.exists(file_path):
                self.emit_log(f"文件不存在: {filename}", 'error')
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.emit_log(f"成功加载文件: {filename}", 'info')
            return data
            
        except Exception as e:
            self.emit_log(f"加载文件失败: {e}", 'error')
            return None
    
    def get_upload_status(self) -> Dict[str, Any]:
        """获取上传状态"""
        return {
            'is_uploading': self.is_uploading,
            'progress': self.current_progress
        }
    
    def get_database_stats(self) -> Optional[Dict[str, Any]]:
        """获取数据库统计信息"""
        connection = self.get_db_connection()
        if not connection:
            return None
        
        try:
            with connection.cursor() as cursor:
                # 获取试卷数量
                cursor.execute("SELECT COUNT(*) as count FROM papers")
                papers_count = cursor.fetchone()['count']
                
                # 获取题目数量
                cursor.execute("SELECT COUNT(*) as count FROM questions")
                questions_count = cursor.fetchone()['count']
                
                # 获取最近的试卷
                cursor.execute("""
                    SELECT paper_name, created_at 
                    FROM papers 
                    ORDER BY created_at DESC 
                    LIMIT 5
                """)
                recent_papers = cursor.fetchall()
                
                return {
                    'papers_count': papers_count,
                    'questions_count': questions_count,
                    'recent_papers': recent_papers
                }
        except Exception as e:
            self.emit_log(f"获取数据库统计信息失败: {e}", 'error')
            return None
        finally:
            connection.close()
