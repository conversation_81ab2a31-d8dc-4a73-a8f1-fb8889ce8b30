import pymysql
import json
import os

# --- 数据库配置 ---
# 这是您提供的配置信息
# 重要安全提示：在实际部署到服务器时，建议将这些敏感信息保存在配置文件或环境变量中，而不是直接写在代码里。
DB_CONFIG = {
    'host': '*************',
    'port': 3306, # MySQL默认端口
    'user': 'Geo_Questions',
    'password': 'Lkr@20020512',
    'database': 'geo_questions',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,
    'connect_timeout': 30,  # 连接超时时间(秒)
    'read_timeout': 60      # 读取超时时间(秒)
}

# --- 文件路径配置 ---
# 请确保这个JSON文件与importer.py脚本在同一个目录下
# 修改JSON_FILE_PATH为绝对路径
JSON_FILE_PATH = os.path.join(os.path.dirname(__file__), '2024年浙江省高考地理真题（6月）.json')


def get_or_create_paper(connection, paper_name: str, source_file: str, image_folder: str) -> int:
    """
    在papers表中获取或创建一条试卷记录，并返回其paper_id。
    【已更新】增加了image_folder参数。
    """
    with connection.cursor() as cursor:
        cursor.execute("SELECT paper_id FROM papers WHERE paper_name = %s", (paper_name,))
        result = cursor.fetchone()
        
        if result:
            paper_id = result['paper_id']
            print(f"试卷 '{paper_name}' 已存在，ID为: {paper_id}")
            # 可选：更新image_folder字段
            cursor.execute("UPDATE papers SET image_folder = %s WHERE paper_id = %s", (image_folder, paper_id))
            print(f"已更新其图片文件夹为: {image_folder}")
            return paper_id
        else:
            # 插入新试卷，【已更新】增加了image_folder
            cursor.execute(
                "INSERT INTO papers (paper_name, source_file, image_folder) VALUES (%s, %s, %s)", 
                (paper_name, source_file, image_folder)
            )
            new_paper_id = cursor.lastrowid
            print(f"新试卷 '{paper_name}' 已创建，ID为: {new_paper_id}")
            return new_paper_id


def insert_questions_to_db(connection, question_list: list, paper_id: int):
    """
    将解析好的题目列表及对应的paper_id写入数据库。
    """
    with connection.cursor() as cursor:
        # SQL插入语句，已包含您新增的 table 字段
        # 使用 ON DUPLICATE KEY UPDATE 避免因重复运行脚本而报错
        sql = """
        INSERT INTO `questions` 
        (`paper_id`, `id`, `group_id`, `is_group_header`, `order_in_group`, `is_subjective`, `source`, 
        `shared_materials`, `image_paths`, `question_text`, `options`, `correct_answer`, `analysis`, `tags`, `table`) 
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        question_text=VALUES(question_text), options=VALUES(options), analysis=VALUES(analysis), `table`=VALUES(`table`);
        """
        
        data_to_insert = []
        for q in question_list:
            # 将列表/字典字段转换为JSON字符串以便存入数据库
            image_paths_json = json.dumps(q.get('image_paths'), ensure_ascii=False) if q.get('image_paths') else None
            options_json = json.dumps(q.get('options'), ensure_ascii=False) if q.get('options') else None
            tags_json = json.dumps(q.get('tags'), ensure_ascii=False) if q.get('tags') else None
            
            data_to_insert.append((
                paper_id,
                q['id'], 
                q['group_id'], 
                q['is_group_header'], 
                q['order_in_group'], 
                q['is_subjective'],
                q.get('source'), 
                q.get('shared_materials'), 
                image_paths_json, 
                q.get('question_text'),
                options_json, 
                q.get('correct_answer'), 
                q.get('analysis'), 
                tags_json,
                q.get('table') # 处理新增的table字段
            ))
            
        if data_to_insert:
            # 批量执行插入
            cursor.executemany(sql, data_to_insert)
            print(f"成功为试卷ID {paper_id} 插入/更新了 {len(data_to_insert)} 条题目数据。")

def main():
    """主程序：读取JSON文件并将内容导入数据库"""
    
    # 1. 读取本地JSON文件
    try:
        with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
            question_data = json.load(f)
        print(f"成功读取JSON文件: {JSON_FILE_PATH}")
    except FileNotFoundError:
        print(f"错误：JSON文件未找到，请确保 '{JSON_FILE_PATH}' 与脚本在同一目录。")
        return
    except json.JSONDecodeError:
        print(f"错误：JSON文件 '{JSON_FILE_PATH}' 格式不正确，请检查。")
        return

    connection = None
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("成功连接到远程MySQL数据库。")
         
        # 3. 获取或创建试卷ID
        paper_name = question_data[0].get('source', '未知试卷') if question_data else '未知试卷'
        
        # 【新增】定义该试卷的图片文件夹名称
        # 这个名称应该与您在服务器上创建的文件夹名一致
        image_folder_name = "2024_zhejiang_jun" # <<--- 您可以根据文件名动态生成这个

        # 【已更新】调用函数时传入文件夹名称
        paper_id = get_or_create_paper(connection, paper_name, JSON_FILE_PATH, image_folder_name)
        
        # 4. 插入所有题目数据 (此部分不变)
        insert_questions_to_db(connection, question_data, paper_id)
        
        connection.commit()
        print("数据库事务已提交，所有数据已成功保存。")

    except pymysql.Error as e:
        print(f"数据库操作失败: {e}")
        if connection:
            connection.rollback() # 如果出错，回滚操作
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        if connection:
            connection.close()
            print("数据库连接已关闭。")

if __name__ == "__main__":
    main()        





    