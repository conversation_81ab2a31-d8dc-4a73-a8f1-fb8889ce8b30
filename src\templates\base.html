<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}地理题库管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #343a40;
        }
        
        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            scroll-behavior: smooth;
        }

        /* 加载动画 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(5px);
        }
        
        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 12px 12px 0 0 !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background-color: var(--success-color); }
        .status-offline { background-color: var(--danger-color); }
        .status-processing { background-color: var(--warning-color); }
        
        .log-container {
            background-color: #1e1e1e;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            border-radius: 8px;
            padding: 15px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            word-wrap: break-word;
        }
        
        .log-timestamp {
            color: #888;
            margin-right: 10px;
        }
        
        .log-info { color: #00ff00; }
        .log-warning { color: #ffaa00; }
        .log-error { color: #ff4444; }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* 深色模式支持 */
        [data-theme="dark"] {
            --light-bg: #1a1a1a;
            --primary-color: #2d3748;
            --secondary-color: #4a5568;
            --dark-bg: #2d3748;
        }

        [data-theme="dark"] body {
            background-color: var(--light-bg);
            color: #e2e8f0;
        }

        [data-theme="dark"] .card {
            background-color: var(--primary-color);
            color: #e2e8f0;
        }

        /* 主题切换按钮 */
        .theme-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--secondary-color);
            border: none;
            color: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('admin_index') }}">
                <i class="fas fa-globe-asia"></i> 地理题库管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <span class="status-indicator status-online" id="connection-status"></span>
                    <span id="connection-text">已连接</span>
                </span>

                {% if current_user.is_authenticated %}
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        {{ current_user.username }}
                        <span class="badge bg-primary ms-1">{{ current_user.role }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">账户信息</h6></li>
                        <li><span class="dropdown-item-text">
                            <small class="text-muted">{{ current_user.email }}</small>
                        </span></li>
                        <li><hr class="dropdown-divider"></li>
                        {% if current_user.role == 'admin' %}
                        <li><a class="dropdown-item" href="{{ url_for('quiz.home') }}">
                            <i class="fas fa-pencil-alt me-2"></i>切换到刷题模式
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        {% endif %}
                        <li><a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_index' %}active{% endif %}" href="{{ url_for('admin_index') }}">
                                <i class="fas fa-tachometer-alt"></i>仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_preprocessor' %}active{% endif %}" href="{{ url_for('admin_preprocessor') }}">
                                <i class="fas fa-file-word"></i>文档预处理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_uploader' %}active{% endif %}" href="{{ url_for('admin_uploader') }}">
                                <i class="fas fa-database"></i>数据库上传
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_pusher' %}active{% endif %}" href="{{ url_for('admin_pusher') }}">
                                <i class="fas fa-paper-plane"></i>推送管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_knowledge_graph' %}active{% endif %}" href="{{ url_for('admin_knowledge_graph') }}">
                                <i class="fas fa-project-diagram"></i>知识图谱
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_config_page' %}active{% endif %}" href="{{ url_for('admin_config_page') }}">
                                <i class="fas fa-cog"></i>系统配置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-10 ms-sm-auto main-content">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="toggleTheme()" title="切换主题">
        <i class="fas fa-moon" id="theme-icon"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Socket.IO 连接管理 -->
    <script>
        const socket = io();
        
        socket.on('connect', function() {
            document.getElementById('connection-status').className = 'status-indicator status-online';
            document.getElementById('connection-text').textContent = '已连接';
        });
        
        socket.on('disconnect', function() {
            document.getElementById('connection-status').className = 'status-indicator status-offline';
            document.getElementById('connection-text').textContent = '连接断开';
        });
        
        socket.on('log', function(data) {
            addLogEntry(data.message, data.level, data.timestamp);
        });
        
        function addLogEntry(message, level, timestamp) {
            const logContainer = document.getElementById('log-container');
            if (logContainer) {
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${level}`;
                logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span>${message}`;
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        }

        // 主题切换功能
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');
            const currentTheme = body.getAttribute('data-theme');

            if (currentTheme === 'dark') {
                body.removeAttribute('data-theme');
                themeIcon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'dark');
            }
        }

        // 页面加载时恢复主题设置
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const themeIcon = document.getElementById('theme-icon');

            if (savedTheme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
                if (themeIcon) themeIcon.className = 'fas fa-sun';
            }

            // 添加页面加载动画
            document.body.classList.add('fade-in');
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
