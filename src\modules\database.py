#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接管理模块
提供数据库连接池和连接管理功能
"""

import pymysql
import logging
import threading
import time
from contextlib import contextmanager
from typing import Optional, Dict, Any
from config.config import Config

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or Config.DB_CONFIG
        self._local = threading.local()
        self._lock = threading.Lock()
        
    def get_connection(self) -> Optional[pymysql.Connection]:
        """获取数据库连接"""
        try:
            # 检查当前线程是否已有连接
            if hasattr(self._local, 'connection') and self._local.connection:
                # 测试连接是否有效
                try:
                    self._local.connection.ping(reconnect=True)
                    return self._local.connection
                except:
                    # 连接无效，清除并重新创建
                    self._local.connection = None
            
            # 创建新连接
            connection = pymysql.connect(**self.config)
            self._local.connection = connection
            logger.debug("创建新的数据库连接")
            return connection
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return None
    
    def close_connection(self):
        """关闭当前线程的数据库连接"""
        if hasattr(self._local, 'connection') and self._local.connection:
            try:
                self._local.connection.close()
                logger.debug("关闭数据库连接")
            except:
                pass
            finally:
                self._local.connection = None
    
    @contextmanager
    def get_cursor(self):
        """获取数据库游标的上下文管理器"""
        connection = self.get_connection()
        if not connection:
            raise Exception("无法获取数据库连接")
        
        cursor = None
        try:
            cursor = connection.cursor()
            yield cursor
        except Exception as e:
            if connection:
                connection.rollback()
            raise e
        finally:
            if cursor:
                cursor.close()
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            connection = self.get_connection()
            if connection:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
        return False
    
    def execute_query(self, sql: str, params: tuple = None) -> Optional[list]:
        """执行查询语句"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return None
    
    def execute_update(self, sql: str, params: tuple = None) -> bool:
        """执行更新语句"""
        try:
            with self.get_cursor() as cursor:
                cursor.execute(sql, params)
                return True
        except Exception as e:
            logger.error(f"更新执行失败: {e}")
            return False

# 全局数据库管理器实例
db_manager = DatabaseManager()

def get_db_connection():
    """获取数据库连接的便捷函数"""
    return db_manager.get_connection()

def test_database_connection() -> bool:
    """测试数据库连接的便捷函数"""
    return db_manager.test_connection()
