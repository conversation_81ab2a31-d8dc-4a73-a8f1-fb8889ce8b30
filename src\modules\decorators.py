"""
装饰器模块
包含权限检查、登录验证等装饰器
"""

from functools import wraps
from flask import redirect, url_for, flash, abort
from flask_login import login_required, current_user
import logging

logger = logging.getLogger(__name__)


def admin_required(f):
    """
    管理员权限装饰器
    要求用户已登录且为管理员角色
    """
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login'))
        
        if not current_user.is_admin():
            logger.warning(f"用户 {current_user.username} 尝试访问管理员功能")
            flash('您没有权限访问此页面', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function


def user_required(f):
    """
    普通用户权限装饰器
    要求用户已登录且为普通用户角色
    """
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('请先登录', 'warning')
            return redirect(url_for('auth.login'))
        
        if current_user.is_admin():
            # 管理员访问用户功能，重定向到管理后台
            return redirect(url_for('index'))
        
        return f(*args, **kwargs)
    return decorated_function


def role_required(role):
    """
    角色权限装饰器工厂
    根据指定角色检查权限
    """
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('请先登录', 'warning')
                return redirect(url_for('auth.login'))
            
            if current_user.role != role:
                logger.warning(f"用户 {current_user.username} (角色: {current_user.role}) 尝试访问 {role} 功能")
                flash('您没有权限访问此页面', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
