---
type: "agent_requested"
description: "Example description"
---
### **`augmentcode` AI 规则集**

**核心原则：修改优先，创造例外 (Modification-First, Creation-as-Exception)**

此规则集指导 AI 在处理代码相关任务时，应始终将修改、扩展或重构现有代码作为首选策略。只有在修改无法满足需求或用户明确指示时，才应考虑创建全新的代码。

---

#### **一、 理解与分析阶段 (Understand & Analyze Phase)**

在进行任何代码操作之前，AI 必须首先执行以下分析：

1.  **意图识别 (Intent Recognition):**
    * **首要任务：** 理解用户请求的核心目标。用户是想修复一个错误、增加一个功能、提高性能，还是改善可读性？
    * **禁止假设：** 如果用户请求模糊（例如“优化这段代码”），应主动询问具体优化目标（“您是想减少执行时间、降低内存占用，还是增强代码的可维护性？”）。

2.  **上下文分析 (Context Analysis):**
    * **代码角色：** 分析目标代码片段在整个项目中的作用、它的输入、输出以及与其他部分的依赖关系。
    * **最小化影响：** 任何修改都应力求对现有系统的影响降到最低。

3.  **结构评估 (Structure Assessment):**
    * **评估可行性：** 判断在当前代码结构上进行修改是否可行、高效。
    * **识别重构机会：** 如果现有代码结构混乱，难以修改，应首先向用户建议进行重构，而不是直接编写新代码。例如，可以提议：“此函数过长，逻辑复杂。在添加新功能前，我建议先将其拆分为几个更小的辅助函数。您同意吗？”

---

#### **二、 执行策略阶段 (Execution Strategy Phase)**

根据分析结果，AI 应按以下优先级选择操作策略：

**优先级 1：微创修改 (Micro-Invasive Modification)**

这是最优先的策略，适用于小范围的、明确的更改。

* **修复 (Fixing):** 直接在原代码中定位并修正 bug。
* **扩展 (Extending):** 在现有函数或类中添加逻辑分支（如 `if/else`）、新的参数（并提供默认值以保证向后兼容）、或错误处理（如 `try/catch` 块）。
* **优化 (Optimizing):** 在不改变外部行为的前提下，替换算法的某个部分或改进数据处理方式。


**优先级 2：结构重构 (Structural Refactoring)**

当微创修改不足以实现目标，或代码质量是主要障碍时，采用此策略。

* **提取 (Extract):** 将函数中的某段代码提取成一个新的、私有的辅助方法。
* **封装 (Encapsulate):** 将重复的逻辑或数据封装到一个新的类中，但必须是在现有代码的上下文中进行，并且替换掉旧的实现。
* **重命名与移动 (Rename & Move):** 为了清晰性而重命名变量、函数或类，或将相关功能移动到更合适的位置。AI 必须确保所有引用都得到同步更新。

**优先级 3：受控创造 (Controlled Creation)**

这是最后的选择，仅在以下情况适用：

* **用户明确指令:** 用户直接要求“创建一个新函数/类/模块来完成...”。
* **功能独立性:** 所需功能与现有代码完全正交，无法通过修改融入。
* **复杂性隔离:** 为了避免让一个已经很复杂的函数变得更加无法维护，可以创建一个新的辅助函数或类来处理新增的复杂逻辑。**但新创建的代码必须立即被原代码调用，以完成整体任务。**

---

#### **三、 边界与约束 (Boundaries & Constraints)**

1.  **禁止随意创建文件:** 除非用户明确要求，否则不得创建新的文件。
2.  **遵守代码风格:** 严格遵守现有代码库的命名约定、格式化风格和设计模式。
3.  **保持接口稳定:** 除非必要，否则不要更改公共函数或方法的签名。如果必须更改，应向用户明确指出这是一个破坏性变更 (Breaking Change)。
4.  **优先使用现有依赖:** 在引入新的第三方库之前，先检查项目中是否已有可以解决问题的库。