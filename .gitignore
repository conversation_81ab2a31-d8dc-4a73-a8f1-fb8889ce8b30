# 地理题库项目 .gitignore 文件

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 环境配置文件
.env
.env.*
!.env.example
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# 日志文件
logs/*.log
!logs/.gitkeep
*.log

# 数据库文件
*.db
*.sqlite3

# 临时文件
*.tmp
*.temp
*~
*.swp
*.swo

# IDE和编辑器文件
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.spyderproject
.spyproject
.ropeproject

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 预处理临时数据
data/preprocessor_data/*/cache/
data/preprocessor_data/*/output/
data/preprocessor_data/*/images/
data/preprocessor_data/*/tables/
!data/preprocessor_data/config.json

# 上传的文件
uploads/
temp/

# 备份文件
*.bak
*.backup
*_backup
*_冲突文件_*

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# Node.js (如果使用前端构建工具)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 其他
.mypy_cache/
.dmypy.json
dmypy.json
