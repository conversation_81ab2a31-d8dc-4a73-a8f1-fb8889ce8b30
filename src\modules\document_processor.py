"""
文档处理核心模块
包含图片提取、表格处理、文档分析等核心功能
"""

import os
import re
import json
import asyncio
import aiohttp
import ssl
import certifi
import zipfile
import xml.etree.ElementTree as ET
import io
import logging
import traceback
import functools
from typing import Dict, List, Any, Tuple, Optional, Callable, TypeVar, Awaitable
from datetime import datetime
from docx import Document
from PIL import Image
from tqdm import tqdm

from .prompts import get_ai_prompt_unified, get_document_analysis_prompt, get_processing_context_info

# 配置日志
logger = logging.getLogger(__name__)

# 函数返回类型
T = TypeVar('T')

# 异常处理装饰器
def handle_exceptions(func: Callable[..., T]) -> Callable[..., T]:
    """装饰器，用于统一处理函数中的异常"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"执行 {func.__name__} 时发生错误: {str(e)}")
            logger.debug(traceback.format_exc())
            raise
    return wrapper

# 异步函数的异常处理装饰器
def async_handle_exceptions(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
    """装饰器，用于统一处理异步函数中的异常"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"执行 {func.__name__} 时发生错误: {str(e)}")
            logger.debug(traceback.format_exc())
            raise
    return wrapper


def get_doc_directories(doc_name: str, base_dir: str) -> Dict[str, str]:
    """为指定文档名创建对应的目录结构并返回路径字典"""
    # 创建文档专属的主目录
    doc_dir = os.path.join(base_dir, doc_name)
    
    # 定义文档专属的子目录
    cache_dir = os.path.join(doc_dir, "cache")
    output_dir = os.path.join(doc_dir, "output")
    img_dir = os.path.join(doc_dir, "images")
    tables_dir = os.path.join(doc_dir, "tables")
    
    # 确保所有目录都存在
    for directory in [doc_dir, cache_dir, output_dir, img_dir, tables_dir]:
        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"创建目录: {directory}")
    
    # 返回目录路径字典
    return {
        "doc_dir": doc_dir,
        "cache_dir": cache_dir,
        "output_dir": output_dir,
        "img_dir": img_dir,
        "tables_dir": tables_dir
    }


@handle_exceptions
def extract_images_from_docx(docx_path: str, base_dir: str) -> Dict[str, Any]:
    """从docx文件中提取图片信息，按照在文档中出现的顺序"""
    logger.info(f"正在提取文档图片: {docx_path}")
    
    # 获取文档名称并创建对应的目录结构
    doc_name = os.path.splitext(os.path.basename(docx_path))[0]
    directories = get_doc_directories(doc_name, base_dir)
    doc_img_dir = directories["img_dir"]
    
    images_info = []
    img_mapping = {}
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as docx_zip:
            # 读取文档内容和关系文件
            document_content = docx_zip.read('word/document.xml').decode('utf-8')
            rels_content = docx_zip.read('word/_rels/document.xml.rels').decode('utf-8')
            
            # 解析关系文件，找出所有图片路径
            rels_root = ET.fromstring(rels_content)
            rel_dict = {}
            
            for child in rels_root:
                if 'Target' in child.attrib and 'Id' in child.attrib:
                    rel_id = child.attrib['Id']
                    target = child.attrib['Target']
                    rel_type = child.attrib.get('Type', '')
                    # 只关注图片/媒体类型
                    if 'media' in target.lower() or 'image' in target.lower() or 'Image' in rel_type or 'picture' in rel_type.lower():
                        rel_dict[rel_id] = target
            
            # 查找文档中的图片引用
            image_refs = []
            # 查找常规图片引用
            image_refs.extend(re.findall(r'<a:blip[^>]*?(?:r:embed|r:link)="(rId\d+)"', document_content))
            # 查找其他可能的图片引用格式
            image_refs.extend(re.findall(r'<pic:blipFill[^>]*>.*?<a:blip[^>]*?(?:r:embed|r:link)="(rId\d+)"', 
                                        document_content, re.DOTALL))
            
            # 移除重复但保持顺序
            unique_refs = []
            seen = set()
            for ref in image_refs:
                if ref not in seen:
                    unique_refs.append(ref)
                    seen.add(ref)
            
            image_refs = unique_refs
            logger.info(f"找到 {len(image_refs)} 个图片引用")
            
            # 提取并保存图片
            img_index = 1
            processed_refs = set()
            
            for ref in image_refs:
                if ref in rel_dict and ref not in processed_refs:
                    try:
                        image_path_in_zip = 'word/' + rel_dict[ref]
                        
                        # 获取图片扩展名
                        file_ext = os.path.splitext(rel_dict[ref])[1].lower() or '.png'
                        if not file_ext.startswith('.'):
                            file_ext = '.' + file_ext
                        
                        img_filename = f"image{img_index}{file_ext}"
                        img_save_path = os.path.join(doc_img_dir, img_filename)
                        
                        # 读取并保存图片
                        with docx_zip.open(image_path_in_zip) as file_in_zip:
                            img_data = file_in_zip.read()
                            with open(img_save_path, 'wb') as file_out:
                                file_out.write(img_data)
                        
                        # 获取图片尺寸
                        width, height = 0, 0
                        try:
                            with Image.open(io.BytesIO(img_data)) as img:
                                width, height = img.size
                        except Exception:
                            pass
                        
                        # 添加图片信息
                        images_info.append({
                            "index": img_index,
                            "filename": img_filename,
                            "path": img_save_path,
                            "width": width,
                            "height": height
                        })
                        
                        img_mapping[img_index] = img_filename
                        processed_refs.add(ref)
                        img_index += 1
                    except Exception as e:
                        logger.warning(f"提取图片 {ref} 时出错: {str(e)}")
    
    except zipfile.BadZipFile:
        logger.error(f"错误: 无效的docx文件或ZIP格式损坏: {docx_path}")
    except Exception as e:
        logger.error(f"提取图片过程中出错: {str(e)}")
        traceback.print_exc()
    
    logger.info(f"成功提取 {len(images_info)} 张图片")
    return {
        "doc_img_dir": doc_img_dir,
        "images": images_info,
        "img_mapping": img_mapping
    }


@handle_exceptions
def read_docx(docx_path: str) -> str:
    """从docx文件中提取纯文本"""
    logger.info(f"正在读取文档: {docx_path}")
    
    # 检查文件类型
    file_ext = os.path.splitext(docx_path)[1].lower()
    
    # 如果不是.docx格式，提示错误
    if file_ext != '.docx':
        raise Exception(f"不支持的文件格式: {file_ext}，仅支持.docx格式")
    
    doc = Document(docx_path)
    content = []
    
    for para in doc.paragraphs:
        text = para.text.strip()
        if text:
            # 规范化空白字符
            text = re.sub(r'\s+', ' ', text)
            # 移除Unicode零宽字符
            text = re.sub(r'[\u200B-\u200D\uFEFF]', '', text)
            content.append(text)
    
    result = "\n".join(content)
    logger.info(f"文档读取完成，共提取 {len(content)} 段落")
    return result


@handle_exceptions
def find_table_belonging_question(table_index: int, table_content: str, question_positions: List[Tuple],
                                analysis: Optional[Dict[str, Any]], table_position_estimate: int, doc) -> Optional[int]:
    """
    Intelligently finds the question ID a table belongs to, considering question type.
    - For Multiple-Choice: Assumes the table is part of the material preceding the question.
    - For Non-Multiple-Choice: Assumes the table follows the question text.

    Args:
        table_index (int): The index of the table (starting from 0).
        table_content (str): The text content of the table.
        question_positions (List[Tuple]): List of (start_idx, q_id, q_text, end_idx).
        analysis (Optional[Dict, str, Any]]): The document analysis result with question types.
        table_position_estimate (int): The accurate paragraph index where the table appears.
        doc: The Document object.

    Returns:
        Optional[int]: The ID of the belonging question.
    """
    if not question_positions:
        return None

    # Create a mapping from a question's start ID to its type for easy lookup
    q_id_to_type = {}
    if analysis and "题组信息" in analysis:
        for group in analysis["题组信息"]:
            try:
                # Handle potential non-integer IDs if necessary, though spec seems to be integer
                start_id = int(group.get("起始题号"))
                q_type = group.get("题型")
                # Apply this type to all questions within the group's range
                end_id = int(group.get("结束题号", start_id))
                for i in range(start_id, end_id + 1):
                    q_id_to_type[i] = q_type
            except (ValueError, TypeError):
                continue

    # Find the last question BEFORE the table and the first question AFTER the table
    last_question_before = None
    first_question_after = None

    # Sort questions by their starting position to ensure correct order
    sorted_questions = sorted(question_positions, key=lambda x: x[0])

    for i, (start_idx, q_id, _, _) in enumerate(sorted_questions):
        if start_idx > table_position_estimate:
            # This is the first question that appears after the table
            first_question_after = (start_idx, q_id)
            # The last question before the table is the one at the previous index
            if i > 0:
                last_question_before = (sorted_questions[i-1][0], sorted_questions[i-1][1])
            break # Exit after finding the first question after the table

    # If the loop finishes, it means the table is after the last question
    if not first_question_after and sorted_questions:
        last_question_before = (sorted_questions[-1][0], sorted_questions[-1][1])

    # --- Logic to decide which question the table belongs to ---

    # Priority 1: Check the type of the question that comes AFTER the table.
    if first_question_after:
        q_id_after = first_question_after[1]
        q_type = q_id_to_type.get(q_id_after, "未知")

        # If it's a multiple-choice question, the table is its introductory material.
        if q_type == "选择题":
            logger.info(f"Table {table_index+1} precedes a Multiple-Choice question (Q{q_id_after}). Assigning table to Q{q_id_after}.")
            return q_id_after

    # Priority 2: If the question after is not multiple-choice, or if there is no question after,
    # the table likely belongs to the PRECEDING question (common for non-multiple-choice).
    if last_question_before:
        q_id_before = last_question_before[1]
        logger.info(f"Table {table_index+1} follows question {q_id_before}. Assigning table to Q{q_id_before}.")
        return q_id_before

    # Fallback: if no other logic works, assign to the closest question
    if first_question_after:
        return first_question_after[1]

    logger.warning(f"Could not determine belonging question for table {table_index+1}.")
    return None


@handle_exceptions
def extract_tables_from_docx(docx_path: str, base_dir: str, analysis: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Extracts all tables from a .docx file, converts them to Markdown, and correctly associates them with questions.
    """
    logger.info(f"Extracting tables from document: {docx_path}")

    tables_info = []
    doc_name = os.path.splitext(os.path.basename(docx_path))[0]
    directories = get_doc_directories(doc_name, base_dir)
    tables_path = directories["tables_dir"]

    if not os.path.exists(tables_path):
        os.makedirs(tables_path)

    try:
        doc = Document(docx_path)

        question_positions = []
        # First, parse all question positions from the document paragraphs.
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            match = re.match(r'^(\d+)[\.。、]', text)
            if match:
                question_id = int(match.group(1))
                end_idx = len(doc.paragraphs) - 1
                for next_q_idx in range(i + 1, len(doc.paragraphs)):
                    next_text = doc.paragraphs[next_q_idx].text.strip()
                    if re.match(r'^(\d+)[\.。、]', next_text):
                        end_idx = next_q_idx - 1
                        break
                question_positions.append((i, question_id, text, end_idx))

        question_positions.sort(key=lambda x: x[0])

        # Now, iterate through the document body to find the actual position of each table.
        para_idx = 0
        table_idx = 0

        # Use doc.element.body to iterate through paragraphs and tables in their actual order
        for block in doc.element.body:
            # The block is either a paragraph or a table
            if block.tag.endswith('p'):
                para_idx += 1
            elif block.tag.endswith('tbl'):
                if table_idx < len(doc.tables):
                    table = doc.tables[table_idx]
                    table_position_estimate = para_idx  # This is the accurate position

                    table_content_text = ""
                    for row in table.rows:
                        for cell in row.cells:
                            table_content_text += cell.text.strip() + " "

                    belonging_question = find_table_belonging_question(
                        table_idx,
                        table_content_text[:200], # Use a snippet for matching
                        question_positions,
                        analysis,
                        table_position_estimate,
                        doc
                    )

                    logger.info(f"Table {table_idx+1} has been assigned to Question ID: {belonging_question}")

                    # --- The rest of your table processing logic remains the same ---
                    table_info = {
                        "index": table_idx + 1,
                        "paragraph_index": table_position_estimate,
                        "question_id": belonging_question,
                        "rows": len(table.rows),
                        "cols": len(table.columns),
                        "markdown": "",
                    }

                    if not table.rows or not table.columns:
                        logger.warning(f"Table {table_idx + 1} is empty, skipping.")
                        table_idx += 1
                        continue

                    # Convert to Markdown
                    markdown_table = []
                    header_row = [re.sub(r'\|', '&#124;', cell.text.strip()) for cell in table.rows[0].cells]
                    markdown_table.append("| " + " | ".join(header_row) + " |")
                    markdown_table.append("| " + " | ".join(["---"] * len(header_row)) + " |")
                    for row in table.rows[1:]:
                        data_row = [re.sub(r'\|', '&#124;', cell.text.strip()) for cell in row.cells]
                        markdown_table.append("| " + " | ".join(data_row) + " |")

                    table_info["markdown"] = "\n".join(markdown_table)

                    table_md_path = os.path.join(tables_path, f"table_{table_info['index']}.md")
                    with open(table_md_path, 'w', encoding='utf-8') as f:
                        f.write(table_info["markdown"])

                    table_info["file_path"] = table_md_path
                    tables_info.append(table_info)

                    table_idx += 1 # Move to the next table

        logger.info(f"Successfully extracted {len(tables_info)} tables.")

    except Exception as e:
        logger.error(f"An error occurred while extracting tables: {str(e)}")
        traceback.print_exc()

    return tables_info


@async_handle_exceptions
async def analyze_questions_async(docx_path: str, base_dir: str, cache_file: Optional[str] = None,
                                use_cache: bool = False, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """分析题目结构，只支持手动分析模式"""
    # 获取文档名称
    doc_name = os.path.splitext(os.path.basename(docx_path))[0]
    directories = get_doc_directories(doc_name, base_dir)

    # 如果未提供缓存文件路径，生成默认路径
    if not cache_file:
        cache_file = os.path.join(directories["cache_dir"], f"{doc_name}_analysis_cache.json")

    # 检查是否使用缓存
    if use_cache and cache_file and os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                # 检查是否为空模板 - 题组信息必须存在且不为空
                题组信息 = cache_data.get("题组信息", [])
                if 题组信息 and len(题组信息) > 0:
                    # 发现有效缓存，抛出异常让用户选择
                    raise CacheFoundException(doc_name, cache_data, cache_file)
        except CacheFoundException:
            # 重新抛出缓存发现异常
            raise
        except Exception:
            pass

    # 始终使用手动处理方式
    return await _manual_analysis_handler(docx_path, base_dir, cache_file)


async def _manual_analysis_handler(docx_path: str, base_dir: str, cache_file: Optional[str] = None) -> Dict[str, Any]:
    """处理手动分析模式，提示用户如何手动获取分析结果"""
    # 提取文档名称
    doc_name = os.path.splitext(os.path.basename(docx_path))[0]
    directories = get_doc_directories(doc_name, base_dir)

    # 提取图片信息
    image_info = extract_images_from_docx(docx_path, base_dir)
    doc_img_dir = image_info["doc_img_dir"]

    # 准备缓存文件
    if not cache_file:
        cache_file = os.path.join(directories["cache_dir"], f"{doc_name}_analysis_cache.json")

    # 如果缓存文件已存在，直接使用
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                analysis_result = json.load(f)
                logger.info(f"已找到现有缓存文件并成功加载: {cache_file}")
                return analysis_result
        except Exception as e:
            logger.error(f"尝试读取缓存文件失败: {str(e)}")

    # 自动创建空的JSON文件结构供用户填写
    empty_template = {
        "题组信息": [],
        "图片分析": {
            "共享材料图片": [],
            "题目图片": {},
            "选项图片": {},
            "未分类图片": []
        },
        "图片目录": doc_img_dir
    }

    # 确保目录存在
    os.makedirs(os.path.dirname(cache_file), exist_ok=True)

    # 保存空模板到缓存文件
    try:
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(empty_template, f, ensure_ascii=False, indent=2)
        logger.info(f"已创建空的分析文件: {cache_file}")
    except Exception as e:
        logger.error(f"创建空的分析文件失败: {str(e)}")

    # 显示手动处理指南
    logger.info("\n" + "="*60)
    logger.info("需要手动分析文档结构")
    logger.info("="*60)
    logger.info(f"1. 将文档 '{docx_path}' 上传到ChatGPT或其他LLM")
    logger.info(f"2. 使用提示词：{get_document_analysis_prompt()}")
    logger.info(f"3. 将AI返回的JSON直接编辑文件：{os.path.abspath(cache_file)}")
    logger.info("="*60)

    # 抛出特殊异常，表示需要手动分析
    raise ManualAnalysisRequiredException(doc_name, get_document_analysis_prompt())


class ManualAnalysisRequiredException(Exception):
    """需要手动分析的异常"""
    def __init__(self, document_name: str, prompt: str):
        self.document_name = document_name
        self.prompt = prompt
        super().__init__(f"Document {document_name} requires manual analysis")


class CacheFoundException(Exception):
    """发现缓存文件的异常"""
    def __init__(self, document_name: str, cache_data: Dict[str, Any], cache_file: str):
        self.document_name = document_name
        self.cache_data = cache_data
        self.cache_file = cache_file
        super().__init__(f"Cache found for document {document_name}")


def generate_basic_question_structure(content: str, question_info: Dict[str, Any], group_index: int) -> str:
    """生成基本的题目结构，直接使用原程序的AI提示词逻辑"""
    start_num = question_info.get('起始题号', str(group_index))
    question_type = question_info.get('题型', '未知')

    # 直接返回提示用户使用AI处理的消息
    result = f"""
题组 {group_index} ({start_num}题, {question_type})
=" * 50
注意: 检测到非选择题，需要AI处理才能正确拆分子题。
请配置API密钥或手动处理。

原始内容:
{content}

建议: 将此内容提交给AI处理，使用统一的AI提示词。
"""
    return result


def extract_sub_questions_from_content(content: str, main_question_id: str) -> List[Tuple[str, str]]:
    """从内容中提取子题目"""
    sub_questions = []

    # 查找多种可能的子题格式：
    # 1. "26(1)" 或 "26（1）" - 完整题号格式
    # 2. "（1）" 或 "(1)" - 仅子题号格式
    patterns = [
        rf'{main_question_id}[\(（](\d+)[\)）]',  # 完整格式：26(1)
        r'[\(（](\d+)[\)）]'  # 仅子题号格式：(1)
    ]

    positions = []

    # 尝试第一种模式（完整格式）
    pattern1 = re.compile(patterns[0])
    matches1 = pattern1.finditer(content)
    for match in matches1:
        sub_num = match.group(1)
        sub_id = main_question_id  # 对于非选择题，所有子题使用相同的主题号
        positions.append((match.start(), sub_id, sub_num, match.group(0)))

    # 如果没找到完整格式，尝试仅子题号格式
    if not positions:
        pattern2 = re.compile(patterns[1])
        matches2 = pattern2.finditer(content)
        for match in matches2:
            sub_num = match.group(1)
            sub_id = main_question_id
            positions.append((match.start(), sub_id, sub_num, match.group(0)))

    if not positions:
        return []

    # 按位置排序
    positions.sort(key=lambda x: x[0])

    # 提取每个子题的内容
    for i, (pos, sub_id, sub_num, matched_text) in enumerate(positions):
        start_pos = pos
        end_pos = positions[i + 1][0] if i + 1 < len(positions) else len(content)

        sub_content = content[start_pos:end_pos].strip()

        # 移除子题号本身，保留题目内容
        # 使用更精确的替换，只替换开头的子题号
        sub_content = sub_content[len(matched_text):].strip()

        if sub_content:
            # 为子题添加格式化的题号
            formatted_content = f"（{sub_num}）{sub_content}"
            sub_questions.append((sub_id, formatted_content))

    return sub_questions


def generate_single_question_structure(question_id: str, group_index: int, question_type: str,
                                     shared_material: str, 图片信息: Dict[str, Any], content: str) -> str:
    """生成单个题目的结构"""
    result = f"<QUESTION_START>\n"
    result += f"<ID>{question_id}</ID>\n"
    result += f"<GROUP_ID>{group_index}</GROUP_ID>\n"
    result += f"<ORDER_IN_GROUP>1</ORDER_IN_GROUP>\n"
    result += f"<IS_GROUP_HEADER>true</IS_GROUP_HEADER>\n"
    result += f"<IS_SUBJECTIVE>{'true' if question_type == '非选择题' else 'false'}</IS_SUBJECTIVE>\n"

    if shared_material:
        result += f"<SHARED_MATERIALS>{shared_material}</SHARED_MATERIALS>\n"
    else:
        result += f"<SHARED_MATERIALS></SHARED_MATERIALS>\n"

    # 添加图片信息
    共享图片 = 图片信息.get('共享图片', [])
    题目图片 = 图片信息.get('题目图片', {}).get(str(question_id), [])
    all_images = 共享图片 + 题目图片
    result += f"<IMAGE_PATHS>{json.dumps(all_images, ensure_ascii=False)}</IMAGE_PATHS>\n"

    result += f"<TEXT>{content}</TEXT>\n"

    if question_type == "非选择题":
        result += f"<OPTIONS></OPTIONS>\n"
    else:
        result += f"<OPTIONS>A. 选项A\nB. 选项B\nC. 选项C\nD. 选项D</OPTIONS>\n"

    result += f"<ANSWER>本小题的答案</ANSWER>\n"
    result += f"<ANALYSIS>本小题的解析</ANALYSIS>\n"
    result += f"<TAGS>知识点标签列表</TAGS>\n"
    result += f"</QUESTION_END>\n\n"

    return result


@async_handle_exceptions
async def process_with_ai_async(question_content: str, question_info: Dict[str, Any],
                              total_groups: int, current_group_index: int, config: Dict[str, Any]) -> str:
    """使用DeepSeek API异步处理题目内容"""
    # 使用统一的提示词文件，不再区分题型
    question_type = question_info.get('题型', '未知')

    # 获取提示词模板 - 直接使用原程序的逻辑
    prompt_template = get_ai_prompt_unified()

    # 构建处理提示词 - 与原程序main.py完全一致
    placeholder = "[在这里粘贴需要处理的试题文本]"
    prompt = prompt_template.replace(placeholder, question_content)

    # 添加图片信息
    image_context_lines = []
    if "图片信息" in question_info:
        img_info = question_info["图片信息"]

        # 处理共享图片
        shared_images = img_info.get("共享图片", [])
        if shared_images:
            # 将列表转换为更易读的字符串
            image_context_lines.append(f"本题组共享的图片有: {', '.join(shared_images)}")

        # 处理题目独有图片
        question_images = img_info.get("题目图片", {})
        if question_images:
            for q_id, img_list in question_images.items():
                image_context_lines.append(f"其中，第 {q_id} 题有其独有的图片: {', '.join(img_list)}")

        # 处理选项图片
        option_images = img_info.get("选项图片", {})
        if option_images:
            image_context_lines.append("此外，部分选项本身就是图片：")
            for option_key, img_list in option_images.items():
                # option_key 可能是 "21_A"，我们解析出题号和选项
                q_id, option = option_key.split('_')
                image_context_lines.append(f"- 第 {q_id} 题的选项 {option} 对应的图片是: {', '.join(img_list)}")

    image_info_str = "\n".join(image_context_lines)

    # 添加处理上下文信息
    shared_materials = question_info.get('共享材料', '')
    context_info = get_processing_context_info(question_info, current_group_index, total_groups, image_info_str)

    # 在提示词中插入上下文信息，安全地检查并替换或添加
    if "请处理以下内容：" in prompt:
        prompt = prompt.replace("请处理以下内容：", f"{context_info}\n请处理以下内容：")
    else:
        # 如果没有找到预期的字符串，将上下文信息添加到提示词的开头
        prompt = f"{context_info}\n{prompt}"

    headers = {
        "Authorization": f"Bearer {config.get('api', {}).get('deepseek_api_key', 'your_api_key_here')}",
        "Content-Type": "application/json"
    }

    data = {
        "model": "deepseek-chat",
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.1,
        "max_tokens": 4096
    }

    # 检查是否验证SSL
    verify_ssl = config.get("verify_ssl", True)

    connector = aiohttp.TCPConnector(
        ssl=ssl.create_default_context(cafile=certifi.where()) if verify_ssl else False
    )

    async with aiohttp.ClientSession(connector=connector) as session:
        async with session.post(f"{config.get('api', {}).get('deepseek_api_base', 'https://api.deepseek.com/v1')}/chat/completions",
                               headers=headers,
                               json=data,
                               timeout=aiohttp.ClientTimeout(total=120)) as response:
            if response.status != 200:
                raise Exception(f"API请求失败: {response.status} - {await response.text()}")

            result = await response.json()
            response_text = result["choices"][0]["message"]["content"]

    return response_text.strip()


def extract_group_content(full_content: str, start_num: str, end_num: str, next_group: Optional[Dict[str, Any]] = None) -> str:
    """从完整内容中提取指定题号范围的内容"""
    # 提取起始题号的主题号（如"26(1)"中的"26"）
    start_main_num_match = re.search(r'^(\d+)', start_num)
    start_main_num = start_main_num_match.group(1) if start_main_num_match else start_num

    # 查找起始题号的位置
    start_patterns = [
        re.compile(rf'{start_main_num}[\.。、]'),  # 普通题号格式：26. 26。 26、
        re.compile(rf'{start_main_num}\s*[\(（]'),  # 子题号格式：26(1) 26（1）
        re.compile(rf'{start_main_num}$')  # 单独的题号
    ]

    start_pos = -1
    for pattern in start_patterns:
        start_match = pattern.search(full_content)
        if start_match:
            start_pos = start_match.start()
            break

    if start_pos == -1:
        return ""

    # 查找结束位置
    end_pos = len(full_content)  # 默认到文档末尾

    # 提取结束题号的主题号
    end_main_num_match = re.search(r'^(\d+)', end_num)
    if end_main_num_match:
        end_main_num = end_main_num_match.group(1)

        # 查找下一个题号
        try:
            next_main_num = str(int(end_main_num) + 1)
            next_pattern = re.compile(rf'{next_main_num}[\.。、]')
            next_match = next_pattern.search(full_content, start_pos)
            if next_match:
                end_pos = next_match.start()
        except ValueError:
            pass

    # 获取提取的内容
    extracted_content = full_content[start_pos:end_pos].strip()

    # 如果有下一题组的信息，检查并移除可能包含的下一题组的材料
    if next_group and "共享材料" in next_group and next_group["共享材料"]:
        next_material = next_group["共享材料"]
        material_identifier = next_material[:min(20, len(next_material))]
        material_pos = extracted_content.find(material_identifier)
        if material_pos != -1:
            extracted_content = extracted_content[:material_pos].strip()

    return extracted_content


def split_and_process_questions(content: str, analysis: Dict[str, Any]) -> List[Tuple[str, Dict]]:
    """根据分析结果拆分题目并处理，按题组拆分"""
    # 获取题组信息
    groups = analysis.get("题组信息", [])
    if not groups:
        return []

    # 按题组拆分
    grouped_questions = []

    for i, group in enumerate(groups):
        start_num = group.get("起始题号", "0")
        end_num = group.get("结束题号", "0")

        if not start_num or not end_num:
            continue

        # 提取这个题组的内容
        next_group = groups[i+1] if i+1 < len(groups) else None
        group_content = extract_group_content(content, start_num, end_num, next_group)
        if group_content:
            grouped_questions.append((group_content, group))

    return grouped_questions


def check_shared_materials(file_path: str, analysis: Dict[str, Any]) -> None:
    """检查文件中的共享材料是否正确"""
    # 读取文件内容
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # 获取题组信息
    groups = analysis.get("题组信息", [])
    if not groups:
        return

    # 创建题号到共享材料的映射
    id_to_materials = {}
    for group in groups:
        start_num = str(group.get("起始题号", ""))
        shared_material = group.get("共享材料", "")
        if start_num and shared_material:
            id_to_materials[start_num] = shared_material

    # 查找所有题目
    questions = re.findall(r'<QUESTION_START>.*?</QUESTION_END>', content, re.DOTALL)

    # 检查每个题目
    for question in questions:
        # 查找题号
        id_match = re.search(r'<ID>(.*?)</ID>', question)
        if not id_match:
            continue

        question_id = id_match.group(1).strip()

        # 查找题型
        type_match = re.search(r'<TYPE>(.*?)</TYPE>', question)
        if not type_match:
            continue

        question_type = type_match.group(1).strip()

        # 查找是否有子题号
        has_sub_id = '<SUB_ID>' in question

        # 查找共享材料
        shared_materials_match = re.search(r'<SHARED_MATERIALS>(.*?)</SHARED_MATERIALS>', question, re.DOTALL)

        # 对于非选择题的第一个块，检查共享材料
        if question_type == "非选择题" and question_id in id_to_materials and not has_sub_id:
            if not shared_materials_match:
                logger.warning(f"题号 {question_id} 中未找到共享材料标签")


def parse_single_field(tag: str, block: str, is_dotall=False) -> str:
    """从文本块中提取单个标签的内容"""
    pattern = re.compile(f'<{tag}>(.*?)</{tag}>', re.DOTALL if is_dotall else 0)
    match = pattern.search(block)
    return match.group(1).strip() if match else ""


def parse_options(options_text: str) -> list:
    """解析OPTIONS块，特殊处理带图片的选项"""
    options_list = []
    # 按换行符分割每个选项
    lines = options_text.strip().split('\n')
    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 匹配选项标签和内容，例如 "A. 选项内容"
        option_match = re.match(r'([A-D])\.\s*(.*)', line, re.DOTALL)
        if not option_match:
            continue

        key = option_match.group(1)
        value_raw = option_match.group(2).strip()

        # 检查选项内容中是否有图片标注
        image_path = None
        image_pattern = r'\(图:\s*(.*?)\)'
        image_match = re.search(image_pattern, value_raw)

        if image_match:
            image_path = image_match.group(1).strip()
            # 从选项文本中移除图片标注
            value = re.sub(image_pattern, '', value_raw).strip()
        else:
            value = value_raw

        options_list.append({
            "key": key,
            "value": value,
            "image_path": image_path
        })
    return options_list


def parse_ai_processed_text(text_content: str, source_name: str, tables_info: Optional[List[Dict[str, Any]]] = None) -> list:
    """
    解析AI生成的、带有自定义标签的完整文本，生成题库对象列表。
    tables_info: 文档中提取的表格信息，用于插入到对应题目的table字段中
    """
    final_question_bank = []

    # 使用正则表达式一次性抽取出所有题目块
    question_blocks = re.findall(r'<QUESTION_START>(.*?)</QUESTION_END>', text_content, re.DOTALL)

    logger.info(f"检测到 {len(question_blocks)} 个题目块，开始解析...")

    # 创建题目ID到表格的映射
    id_to_tables = {}
    if tables_info:
        for table in tables_info:
            question_id = table.get("question_id")
            if question_id:
                if question_id not in id_to_tables:
                    id_to_tables[question_id] = []
                id_to_tables[question_id].append(table)

    # 创建group_id到表格的映射，用于后续给题组头添加表格
    group_tables = {}

    for block in question_blocks:
        try:
            # --- 提取所有字段的原始文本 ---
            q_id_str = parse_single_field('ID', block)
            group_id_str = parse_single_field('GROUP_ID', block)
            order_in_group_str = parse_single_field('ORDER_IN_GROUP', block)
            is_group_header_str = parse_single_field('IS_GROUP_HEADER', block)
            is_subjective_str = parse_single_field('IS_SUBJECTIVE', block)
            shared_materials = parse_single_field('SHARED_MATERIALS', block, is_dotall=True)
            image_paths_str = parse_single_field('IMAGE_PATHS', block)
            question_text = parse_single_field('TEXT', block, is_dotall=True)
            options_text = parse_single_field('OPTIONS', block, is_dotall=True)
            correct_answer = parse_single_field('ANSWER', block, is_dotall=True)
            analysis = parse_single_field('ANALYSIS', block, is_dotall=True)
            tags_str = parse_single_field('TAGS', block)

            # --- 对提取的文本进行类型转换和处理 ---
            q_id = int(q_id_str)
            group_id = int(group_id_str)
            is_group_header = is_group_header_str.lower() == 'true'

            # 创建基础题目对象
            question_obj = {
                "id": q_id,
                "group_id": group_id,
                "is_group_header": is_group_header,
                "order_in_group": int(order_in_group_str),
                "is_subjective": is_subjective_str.lower() == 'true',
                "source": source_name,
                "shared_materials": shared_materials or None,
                "image_paths": json.loads(image_paths_str) if image_paths_str and image_paths_str.startswith('[') else [],
                "question_text": question_text,
                "options": parse_options(options_text) if not (is_subjective_str.lower() == 'true') else None,
                "correct_answer": correct_answer,
                "analysis": analysis,
                "tags": [tag.strip() for tag in tags_str.split(',')] if tags_str else []
            }

            # 检查这个题目是否有关联的表格，只在题组头(is_group_header=true)添加表格
            if q_id in id_to_tables:
                # 获取该题目的所有表格
                question_tables = id_to_tables[q_id]

                # 如果是题组头，才添加表格
                if is_group_header and question_tables:
                    table_md_content = []
                    for table in question_tables:
                        # 添加表格标题
                        table_md_content.append(f"\n\n**表格 {table['index']}**\n")
                        # 添加表格内容
                        table_md_content.append(table['markdown'])

                    # 设置表格内容
                    if table_md_content:
                        question_obj["table"] = "".join(table_md_content).strip()
                        logger.info(f"题目 {q_id} 添加了 {len(question_tables)} 个表格到table字段")

                # 如果不是题组头但有表格，记录表格应该添加到哪个组的题组头
                elif not is_group_header:
                    # 将表格信息保存到组映射中，以便后续在组头中添加
                    if group_id not in group_tables:
                        group_tables[group_id] = []
                    group_tables[group_id].extend(question_tables)

            final_question_bank.append(question_obj)
        except Exception as e:
            logger.error(f"解析题目块失败: {e}\n问题块内容预览: \n{block[:300]}...")
            continue

    # 第二次遍历，将非题组头的表格添加到对应组的题组头中
    for question in final_question_bank:
        group_id = question["group_id"]
        if question["is_group_header"] and group_id in group_tables:
            # 如果是题组头并且该组有从非题组头收集的表格
            tables = group_tables[group_id]

            # 准备添加表格内容
            table_md_content = []
            if "table" in question:  # 如果已经有表格，继续添加
                existing_content = question["table"]
                if existing_content:
                    table_md_content.append(existing_content)

            # 添加从非题组头收集的表格
            for table in tables:
                # 添加表格标题
                table_md_content.append(f"\n\n**表格 {table['index']}**\n")
                # 添加表格内容
                table_md_content.append(table['markdown'])

            # 更新表格内容
            if table_md_content:
                question["table"] = "\n".join(table_md_content).strip()
                logger.info(f"题组头 {question['id']} (组 {group_id}) 添加了从非题组头收集的表格")

    return final_question_bank


def convert_processed_to_json(text_file_path: str, source_name: str, doc_name: str, base_dir: str,
                            tables_info: Optional[List[Dict[str, Any]]] = None) -> Optional[str]:
    """将处理后的文本文件转换为JSON题库文件"""
    # 获取文档对应的目录
    directories = get_doc_directories(doc_name, base_dir)
    json_file_path = os.path.join(directories["output_dir"], f"{doc_name}.json")

    logger.info(f"开始将格式化文本转换为JSON题库: {text_file_path}")

    # 读取AI处理后的文本文件
    try:
        with open(text_file_path, 'r', encoding='utf-8') as f:
            processed_text = f.read()
        logger.info(f"成功读取文件: {text_file_path}")
    except FileNotFoundError:
        logger.error(f"输入文件不存在 {text_file_path}")
        return None

    # 解析文本，生成Python对象列表
    question_bank_data = parse_ai_processed_text(processed_text, source_name, tables_info)

    # 将结果保存为最终的JSON文件
    if question_bank_data:
        try:
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(question_bank_data, f, ensure_ascii=False, indent=2)
            logger.info(f"处理成功！JSON题库已生成: {json_file_path}")
            logger.info(f"共包含 {len(question_bank_data)} 道题目")
            return json_file_path
        except Exception as e:
            logger.error(f"写入JSON文件失败: {e}")

    return None


# 应用异步装饰器到主要处理函数
@async_handle_exceptions
async def process_file_async(docx_path: str, base_dir: str, config: Dict[str, Any], skip_cache_check: bool = False, progress_callback=None) -> Tuple[str, Optional[str]]:
    """异步处理单个文档并返回输出路径"""
    # 获取文档名称并创建对应目录结构
    base_name = os.path.splitext(os.path.basename(docx_path))[0]
    directories = get_doc_directories(base_name, base_dir)

    # 创建默认输出路径和缓存文件路径
    output_path = os.path.join(directories["output_dir"], f"{base_name}_processed.txt")
    cache_file = os.path.join(directories["cache_dir"], f"{base_name}_analysis_cache.json")

    logger.info(f"处理文档: {docx_path}")

    if progress_callback:
        progress_callback(35, "分析题目结构...")

    # 先分析题目结构，使用缓存如果配置允许
    if skip_cache_check:
        # 直接读取缓存文件，不进行缓存检查
        if os.path.exists(cache_file):
            with open(cache_file, 'r', encoding='utf-8') as f:
                analysis = json.load(f)
        else:
            raise Exception(f"缓存文件不存在: {cache_file}")
    else:
        use_cache = config.get("use_cache", True)
        analysis = await analyze_questions_async(docx_path, base_dir, cache_file=cache_file, use_cache=use_cache, config=config)

    if progress_callback:
        progress_callback(45, "提取表格信息...")

    # 在获取题目分析结果后提取表格，这样可以利用题目类型信息更准确地分配表格
    tables_info = extract_tables_from_docx(docx_path, base_dir, analysis)

    # 将表格信息添加到分析结果中
    analysis["表格信息"] = tables_info

    # 保存更新后的分析结果到缓存
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)

    if progress_callback:
        progress_callback(55, "读取文档内容...")

    # 读取文档内容
    content = read_docx(docx_path)

    if progress_callback:
        progress_callback(60, "拆分题目...")

    # 拆分并处理题目
    grouped_questions = split_and_process_questions(content, analysis)
    total_groups = len(grouped_questions)

    if total_groups == 0:
        logger.warning("未识别到题组，需要手动分析")
        # 抛出手动分析异常
        doc_name = os.path.splitext(os.path.basename(docx_path))[0]
        from .prompts import get_document_analysis_prompt
        raise ManualAnalysisRequiredException(doc_name, get_document_analysis_prompt())

    if progress_callback:
        progress_callback(65, f"开始处理 {total_groups} 个题组...")

    # 直接使用AI处理，不检查API密钥（与原程序main.py保持一致）
    # 创建进度条
    progress_bar = tqdm(total=total_groups, desc="AI处理进度")

    # 并发处理结果列表，保持原始顺序
    processed_results = [""] * total_groups

    # 使用信号量限制并发数
    semaphore = asyncio.Semaphore(5)

    async def process_with_semaphore(index, content_info):
        async with semaphore:
            content, info = content_info
            # 使用与原程序相同的函数签名
            result = await process_with_ai_async(content, info, total_groups, index, config)
            processed_results[index] = result
            # 更新进度条
            progress_bar.update(1)

            # 报告进度到网页
            if progress_callback:
                progress_percentage = 65 + (index + 1) / total_groups * 20  # 65% 到 85%
                progress_callback(progress_percentage, f"AI处理题组 {index + 1}/{total_groups}")

            return index, result

    # 创建所有任务
    tasks = [process_with_semaphore(i, q) for i, q in enumerate(grouped_questions)]

    # 等待所有任务完成
    await asyncio.gather(*tasks)
    progress_bar.close()

    # 合并处理结果，保持原始顺序
    final_result = "\n\n".join(processed_results)

    if progress_callback:
        progress_callback(85, "保存处理结果...")

    # 保存到文件
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(final_result)

    # 检查文件中的共享材料
    check_shared_materials(output_path, analysis)

    if progress_callback:
        progress_callback(88, "生成JSON题库...")

    # 将处理后的文本转换为JSON题库，传递表格信息
    source_name = base_name.split("_")[0]  # 假设第一部分是试卷名称
    json_file_path = convert_processed_to_json(output_path, source_name, base_name, base_dir, tables_info)

    if progress_callback:
        progress_callback(90, "文档处理完成")

    return output_path, json_file_path


@async_handle_exceptions
async def process_files_async(files: List[str], docx_dir: str, base_dir: str, config: Dict[str, Any]) -> List[Tuple[str, str, Optional[str]]]:
    """异步处理多个文件"""
    processed_files = []

    # 检查docx目录是否存在
    if not os.path.exists(docx_dir):
        logger.error(f"docx目录不存在: {docx_dir}")
        return processed_files

    for file_name in files:
        file_path = os.path.join(docx_dir, file_name)
        if not os.path.exists(file_path):
            logger.error(f"文件 {file_path} 不存在")
            continue

        try:
            output_path, json_path = await process_file_async(file_path, base_dir, config)
            processed_files.append((file_name, output_path, json_path))
        except CacheFoundException as e:
            # 重新抛出缓存发现异常，让上层处理
            logger.info(f"文件 {file_name} 发现缓存文件")
            raise e
        except ManualAnalysisRequiredException as e:
            # 重新抛出手动分析异常，让上层处理
            logger.info(f"文件 {file_name} 需要手动分析")
            raise e
        except Exception as e:
            logger.error(f"处理文件 {file_name} 时发生错误: {e}")

    return processed_files
