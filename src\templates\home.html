<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地理题库系统 - 智能地理学习平台</title>
    <meta name="description" content="专业的地理题库管理系统，支持在线刷题、智能解析、题目管理，为教育工作者和学习者提供高效的学习解决方案">
    <meta name="keywords" content="地理题库,在线刷题,教育系统,题目管理,AI解析,地理学习">

    <!-- 资源预加载 -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font <PERSON> (统一使用一套图标库) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            scroll-behavior: smooth;
        }

        /* 动画效果 */
        .animate-in {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 加载动画 */
        .loading {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease-out;
        }

        .loaded {
            opacity: 1;
            transform: translateY(0);
        }
        
        .hero-section {
            background: var(--gradient-bg);
            color: white;
            padding: 100px 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-bg);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }
        
        .btn-custom {
            background: var(--gradient-bg);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            color: white;
        }
        
        .stats-section {
            background: var(--light-bg);
            padding: 80px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: var(--secondary-color);
            display: block;
        }
        
        .navbar-custom {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }
        
        .footer {
            background: var(--primary-color);
            color: white;
            padding: 40px 0;
            margin-top: 80px;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .hero-section {
                padding: 60px 0;
            }

            .hero-section h1 {
                font-size: 2.5rem;
            }

            .feature-card {
                margin-bottom: 20px;
                padding: 20px;
            }

            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .stats-section .stat-number {
                font-size: 2.5rem;
            }

            .btn-custom {
                min-height: 44px;
                min-width: 120px;
                margin: 5px;
            }

            .navbar-brand {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 576px) {
            .hero-section {
                padding: 40px 0;
            }

            .hero-section h1 {
                font-size: 2rem;
            }

            .hero-section .lead {
                font-size: 1rem;
            }

            .feature-card {
                padding: 15px;
            }

            .stats-section .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-globe-asia me-2"></i>
                地理题库系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">功能特色</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">关于系统</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-primary ms-2 px-3" href="/auth/login">
                            <i class="fas fa-sign-in-alt me-1"></i>登录
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-section">
        <div class="container hero-content">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">
                        智能地理学习平台
                    </h1>
                    <p class="lead mb-4">
                        集成文档处理、题库管理、在线刷题于一体的现代化地理学习系统。
                        支持AI辅助题目解析，提供个性化学习体验。
                    </p>
                    <div class="d-flex flex-wrap gap-3">
                        <a href="/auth/login" class="btn-custom">
                            <i class="fas fa-play me-2"></i>开始学习
                        </a>
                        <a href="#features" class="btn btn-outline-light">
                            <i class="fas fa-info-circle me-2"></i>了解更多
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-image">
                        <i class="fas fa-graduation-cap" style="font-size: 15rem; opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 统计数据 -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <i class="fas fa-question-circle text-primary mb-2" style="font-size: 2rem;"></i>
                        <span class="stat-number">1000+</span>
                        <p class="text-muted">精选题目</p>
                        <small class="text-success">
                            <i class="fas fa-arrow-up"></i> 持续更新中
                        </small>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <i class="fas fa-tags text-info mb-2" style="font-size: 2rem;"></i>
                        <span class="stat-number">50+</span>
                        <p class="text-muted">知识点覆盖</p>
                        <small class="text-info">
                            <i class="fas fa-chart-line"></i> 全面覆盖
                        </small>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <i class="fas fa-robot text-warning mb-2" style="font-size: 2rem;"></i>
                        <span class="stat-number">AI</span>
                        <p class="text-muted">智能解析</p>
                        <small class="text-warning">
                            <i class="fas fa-brain"></i> 智能驱动
                        </small>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <i class="fas fa-clock text-success mb-2" style="font-size: 2rem;"></i>
                        <span class="stat-number">24/7</span>
                        <p class="text-muted">在线服务</p>
                        <small class="text-success">
                            <i class="fas fa-check-circle"></i> 随时可用
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能特色 -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="display-5 fw-bold mb-3">功能特色</h2>
                    <p class="lead text-muted">为不同用户群体提供专业的地理学习解决方案</p>
                </div>
            </div>
            
            <div class="row g-4">
                <!-- 学生用户功能 -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h4 class="mb-3">在线刷题系统</h4>
                        <p class="text-muted mb-4">
                            支持随机刷题和分类练习，实时统计答题情况，
                            自动生成错题本，个性化学习路径推荐。
                        </p>
                        <ul class="list-unstyled text-start">
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>智能题目推荐</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>答题历史记录</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>错题本管理</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>学习进度跟踪</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 教师管理功能 -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h4 class="mb-3">智能题库管理</h4>
                        <p class="text-muted mb-4">
                            AI辅助文档解析，自动提取题目和图片，
                            支持批量导入，智能分类管理。
                        </p>
                        <ul class="list-unstyled text-start">
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>文档智能解析</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>批量题目导入</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>图片自动提取</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>题目分类管理</li>
                        </ul>
                    </div>
                </div>
                
                <!-- 系统特色 -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h4 class="mb-3">系统特色功能</h4>
                        <p class="text-muted mb-4">
                            现代化的Web界面，响应式设计，
                            支持多平台访问，数据安全可靠。
                        </p>
                        <ul class="list-unstyled text-start">
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>响应式设计</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>实时数据同步</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>多用户权限管理</li>
                            <li><i class="bi bi-check-circle-fill text-success me-2"></i>数据安全保护</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 关于系统 -->
    <section id="about" class="py-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="display-5 fw-bold mb-4">关于地理题库系统</h2>
                    <p class="lead mb-4">
                        本系统是一个专为地理学科设计的现代化学习管理平台，
                        集成了最新的AI技术和Web开发技术。
                    </p>
                    <div class="row g-3">
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-robot text-primary me-3" style="font-size: 1.5rem;"></i>
                                <div>
                                    <h6 class="mb-1">AI智能解析</h6>
                                    <small class="text-muted">自动识别题目结构</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-mobile-alt text-primary me-3" style="font-size: 1.5rem;"></i>
                                <div>
                                    <h6 class="mb-1">移动端适配</h6>
                                    <small class="text-muted">随时随地学习</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-shield-alt text-primary me-3" style="font-size: 1.5rem;"></i>
                                <div>
                                    <h6 class="mb-1">数据安全</h6>
                                    <small class="text-muted">多重安全保护</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-chart-line text-primary me-3" style="font-size: 1.5rem;"></i>
                                <div>
                                    <h6 class="mb-1">学习分析</h6>
                                    <small class="text-muted">详细学习报告</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="p-4">
                        <i class="fas fa-laptop-code" style="font-size: 12rem; color: var(--secondary-color); opacity: 0.3;"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">
                        <i class="fas fa-globe-asia me-2"></i>
                        地理题库系统
                    </h5>
                    <p class="text-light">
                        专业的地理学习管理平台，为教育工作者和学习者提供
                        高效、智能的题库管理和学习解决方案。
                    </p>
                </div>
                <div class="col-md-6">
                    <h6 class="mb-3">快速链接</h6>
                    <div class="row">
                        <div class="col-6">
                            <ul class="list-unstyled">
                                <li><a href="/auth/login" class="text-light text-decoration-none">用户登录</a></li>
                                <li><a href="/auth/register" class="text-light text-decoration-none">用户注册</a></li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <ul class="list-unstyled">
                                <li><a href="/admin" class="text-light text-decoration-none">管理后台</a></li>
                                <li><a href="/quiz/home" class="text-light text-decoration-none">在线刷题</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 地理题库系统. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-light">基于 Flask + Bootstrap 构建</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 增强交互脚本 -->
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载动画
            const elements = document.querySelectorAll('.feature-card, .stat-item');
            elements.forEach((el, index) => {
                el.classList.add('loading');
                setTimeout(() => {
                    el.classList.remove('loading');
                    el.classList.add('loaded');
                }, index * 100);
            });

            // 数字动画效果
            animateNumbers();

            // 设置滚动观察器
            setupScrollAnimations();
        });

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-custom');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255,255,255,0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0,0,0,0.15)';
            } else {
                navbar.style.background = 'rgba(255,255,255,0.95)';
                navbar.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
            }
        });

        // 数字动画函数
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(num => {
                const text = num.textContent;
                const target = parseInt(text.replace(/\D/g, '')) || 0;
                if (target > 0) {
                    let current = 0;
                    const increment = target / 50;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            current = target;
                            clearInterval(timer);
                        }
                        num.textContent = Math.floor(current) + (text.includes('+') ? '+' : '');
                    }, 30);
                }
            });
        }

        // 滚动动画设置
        function setupScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, observerOptions);

            // 观察需要动画的元素
            document.querySelectorAll('.feature-card, .stat-item').forEach(el => {
                observer.observe(el);
            });
        }

        // 按钮点击效果
        document.querySelectorAll('.btn-custom').forEach(btn => {
            btn.addEventListener('click', function(e) {
                // 创建波纹效果
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255,255,255,0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;

                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // 添加波纹动画CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
