{% extends "base.html" %}

{% block title %}知识图谱 - 地理题库管理系统{% endblock %}

{% block extra_css %}
<style>
    .graph-container {
        position: relative;
        width: 100%;
        height: 600px;
        border: none;
        border-radius: 16px;
        overflow: hidden;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    .filter-panel {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .filter-panel .form-label {
        color: white;
        font-weight: 600;
    }
    
    .filter-panel .form-select,
    .filter-panel .form-control {
        border: none;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .graph-info {
        position: absolute;
        top: 15px;
        right: 15px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        padding: 15px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        min-width: 140px;
        font-size: 13px;
    }

    /* 节点详情面板样式 */
    .node-details-panel {
        position: fixed;
        top: 80px;
        left: 20px;
        z-index: 9999;
        background: rgba(255, 255, 255, 0.98);
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
        min-width: 280px;
        max-width: 400px;
        max-height: 500px;
        overflow-y: auto;
        display: none !important;
        border: 2px solid #667eea;
        transform: translateX(-20px);
        opacity: 0;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .node-details-panel.show {
        display: block !important;
        transform: translateX(0);
        opacity: 1;
    }

    .node-details-panel .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
    }

    .node-details-panel .panel-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .node-details-panel .close-btn {
        background: none;
        border: none;
        font-size: 18px;
        color: #999;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .node-details-panel .close-btn:hover {
        color: #666;
    }

    .node-property {
        margin-bottom: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #f5f5f5;
    }

    .node-property:last-child {
        border-bottom: none;
    }

    .property-label {
        font-weight: 600;
        color: #555;
        font-size: 12px;
        text-transform: uppercase;
        margin-bottom: 4px;
    }

    .property-value {
        color: #333;
        font-size: 14px;
        word-break: break-word;
    }

    .connections-section {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 2px solid #f0f0f0;
    }

    .connections-title {
        font-weight: 600;
        color: #667eea;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .connection-item {
        padding: 6px 10px;
        margin-bottom: 5px;
        background: #f8f9ff;
        border-radius: 6px;
        font-size: 13px;
        color: #555;
    }
    
    .connection-status {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
        background-color: #28a745;
    }
    
    #pyvis-graph {
        width: 100%;
        height: 100%;
        border-radius: 16px;
    }
    
    .loading {
        text-align: center;
        padding: 50px;
        color: #6C5CE7;
        font-size: 18px;
    }
    
    .error-message {
        text-align: center;
        padding: 50px;
        color: #E17055;
        font-size: 16px;
    }
    
    .physics-info {
        position: absolute;
        bottom: 15px;
        left: 15px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        padding: 10px 15px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        font-size: 12px;
        color: #6C5CE7;
    }

    /* 搜索面板样式 */
    .search-panel {
        position: absolute;
        top: 15px;
        left: 15px;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.95);
        padding: 15px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        min-width: 300px;
    }

    .search-input {
        border: none;
        border-radius: 8px;
        padding: 8px 12px;
        width: 100%;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .search-input:focus {
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        transform: translateY(-1px);
    }

    .search-results {
        max-height: 200px;
        overflow-y: auto;
        margin-top: 10px;
        border-radius: 8px;
        background: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .search-result-item {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s ease;
    }

    .search-result-item:hover {
        background-color: #f8f9fa;
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    /* 重复的节点详情面板CSS已移除 */

    .node-details-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
    }

    .node-details-title {
        font-size: 18px;
        font-weight: bold;
        color: #2D3436;
        margin: 0;
    }

    .node-details-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #636e72;
        padding: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .node-details-close:hover {
        background-color: #f8f9fa;
        color: #2D3436;
    }

    .node-property {
        margin-bottom: 12px;
        padding: 8px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .node-property:last-child {
        border-bottom: none;
    }

    .property-label {
        font-weight: 600;
        color: #6C5CE7;
        font-size: 14px;
        margin-bottom: 4px;
    }

    .property-value {
        color: #2D3436;
        font-size: 14px;
        word-wrap: break-word;
    }

    .connections-section {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 2px solid #f0f0f0;
    }

    .connections-title {
        font-size: 16px;
        font-weight: bold;
        color: #2D3436;
        margin-bottom: 10px;
    }

    .connection-item {
        padding: 6px 10px;
        margin: 4px 0;
        background: #f8f9fa;
        border-radius: 6px;
        font-size: 13px;
        color: #636e72;
    }

    /* 高级筛选样式 */
    .advanced-filters {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
    }

    .filter-group {
        margin-bottom: 15px;
    }

    .filter-group:last-child {
        margin-bottom: 0;
    }

    .filter-title {
        font-size: 14px;
        font-weight: 600;
        color: #2D3436;
        margin-bottom: 8px;
    }

    .checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .checkbox-item {
        display: flex;
        align-items: center;
        background: white;
        padding: 6px 10px;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
    }

    .checkbox-item:hover {
        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }

    .checkbox-item input[type="checkbox"] {
        margin-right: 6px;
    }

    .checkbox-item label {
        font-size: 13px;
        color: #2D3436;
        margin: 0;
        cursor: pointer;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        margin: 5px 0;
    }
    
    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-project-diagram"></i> 知识图谱</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" onclick="refreshGraph()">
            <i class="fas fa-sync-alt"></i> 刷新图谱
        </button>
        <button type="button" class="btn btn-outline-info ms-2" data-bs-toggle="modal" data-bs-target="#legendModal">
            <i class="fas fa-info-circle"></i> 图例
        </button>
    </div>
</div>

<!-- 筛选面板 -->
<div class="filter-panel">
    <form method="GET" action="{{ url_for('admin_knowledge_graph') }}" id="filterForm">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">节点类型筛选</label>
                <select class="form-select" name="node_type">
                    <option value="">所有类型</option>
                    {% if node_types %}
                        {% for node_type in node_types %}
                            <option value="{{ node_type }}" {% if node_type == current_node_type %}selected{% endif %}>
                                {{ node_type }}
                            </option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">关系类型筛选</label>
                <select class="form-select" name="relationship_type">
                    <option value="">所有关系</option>
                    {% if relationship_types %}
                        {% for rel_type in relationship_types %}
                            <option value="{{ rel_type }}" {% if rel_type == current_relationship_type %}selected{% endif %}>
                                {{ rel_type }}
                            </option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">显示数量限制</label>
                <input type="number" class="form-control" name="limit" value="{{ current_limit or 100 }}" min="10" max="1000">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-light me-2">
                    <i class="fas fa-filter"></i> 应用筛选
                </button>
                <button type="button" class="btn btn-outline-light me-2" onclick="clearFilters()">
                    <i class="fas fa-times"></i> 清除
                </button>
                <button type="button" class="btn btn-info" onclick="toggleAdvancedFilters()">
                    <i class="fas fa-cogs"></i> <span id="advancedToggleText">显示高级筛选</span>
                </button>
            </div>
        </div>

        <!-- 高级筛选选项 -->
        <div class="advanced-filters" id="advancedFilters" style="display: none;">
            <div class="row">
                <div class="col-md-6">
                    <div class="filter-group">
                        <div class="filter-title">节点类型 (多选)</div>
                        <div class="checkbox-group" id="nodeTypeCheckboxes">
                            {% if node_types %}
                                {% for node_type in node_types %}
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="nodeType_{{ loop.index }}" value="{{ node_type }}" class="node-type-filter">
                                        <label for="nodeType_{{ loop.index }}">{{ node_type }}</label>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="filter-group">
                        <div class="filter-title">关系类型 (多选)</div>
                        <div class="checkbox-group" id="relationshipTypeCheckboxes">
                            {% if relationship_types %}
                                {% for rel_type in relationship_types %}
                                    <div class="checkbox-item">
                                        <input type="checkbox" id="relType_{{ loop.index }}" value="{{ rel_type }}" class="relationship-type-filter">
                                        <label for="relType_{{ loop.index }}">{{ rel_type }}</label>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button type="button" class="btn btn-primary btn-sm" onclick="applyAdvancedFilters()">
                    <i class="fas fa-check"></i> 应用高级筛选
                </button>
                <button type="button" class="btn btn-secondary btn-sm" onclick="clearAdvancedFilters()">
                    <i class="fas fa-times"></i> 清除筛选
                </button>
            </div>
        </div>

        <!-- 重复的高级筛选按钮已移除 -->
    </form>
</div>

<!-- 图谱容器 -->
<div class="graph-container">
    <!-- 节点详情面板 -->
    <div class="node-details-panel" id="nodeDetailsPanel">
        <div class="panel-header">
            <h6 class="panel-title" id="nodeDetailsTitle">节点详情</h6>
            <button class="close-btn" onclick="hideNodeDetails()" title="关闭">×</button>
        </div>
        <div id="nodeDetailsContent">
            <!-- 节点详情内容将在这里动态生成 -->
        </div>
    </div>

    <!-- 搜索面板 -->
    <div class="search-panel">
        <div class="d-flex align-items-center mb-2">
            <i class="fas fa-search text-muted me-2"></i>
            <input type="text" class="search-input" id="nodeSearchInput" placeholder="搜索节点或关系... (Ctrl+F)" autocomplete="off">
        </div>
        <div class="search-results" id="searchResults" style="display: none;"></div>
    </div>

    <!-- 连接状态信息 -->
    <div class="graph-info">
        <div class="d-flex align-items-center mb-2">
            <span class="connection-status" id="connectionStatus"></span>
            <small id="connectionText">检查Neo4j连接...</small>
        </div>
        <div class="mb-2">
            <small>📊 节点数量: <span id="nodeCount">加载中...</span></small><br>
            <small>🔗 边数量: <span id="edgeCount">加载中...</span></small>
        </div>
        <div class="mb-2">
            <small id="dataSource">数据源: 检查中...</small>
        </div>
        <div>
            <small>力导向布局</small><br>
            <small>PyVis物理引擎</small>
        </div>
    </div>

    <!-- 重复的节点详情面板已移除 -->

    <!-- PyVis图谱内容 -->
    <div id="pyvis-graph">
        {% if error %}
            <div class="error-message" style="text-align: center; padding: 50px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 20px;"></i><br>
                <h4>Neo4j数据库连接失败</h4>
                <p style="margin: 20px 0;">{{ error }}</p>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left;">
                    <strong>解决方案：</strong><br>
                    1. 确保Neo4j数据库服务正在运行<br>
                    2. 检查连接配置（默认: neo4j://localhost:7687）<br>
                    3. 验证用户名和密码（默认: neo4j/12345678）<br>
                    4. 确保数据库中有数据<br>
                    5. 安装py2neo: <code>pip install py2neo</code>
                </div>
                <button class="btn btn-primary" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt"></i> 重新连接
                </button>
            </div>
        {% elif graph_html %}
            {{ graph_html|safe }}
        {% else %}
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i><br>
                正在生成力导向知识图谱...
            </div>
        {% endif %}
    </div>

    <!-- 物理引擎信息 -->
    <div class="physics-info">
        <i class="fas fa-atom"></i> 物理模拟：弹簧力 + 电荷斥力
    </div>
</div>

<!-- 图例模态框 -->
<div class="modal fade" id="legendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">知识图谱图例</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>节点类型</h6>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #6C5CE7;"></div>
                            <span>地理概念</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #00B894;"></div>
                            <span>知识点</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #0984E3;"></div>
                            <span>题目</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #E17055;"></div>
                            <span>试卷</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>关系类型</h6>
                        <div class="legend-item">
                            <div style="width: 20px; height: 3px; background-color: #0984E3; margin-right: 8px;"></div>
                            <span>包含</span>
                        </div>
                        <div class="legend-item">
                            <div style="width: 20px; height: 2px; background-color: #E17055; margin-right: 8px;"></div>
                            <span>关联</span>
                        </div>
                        <div class="legend-item">
                            <div style="width: 20px; height: 2px; background-color: #00B894; margin-right: 8px;"></div>
                            <span>属于</span>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        图谱使用PyVis物理引擎，节点会通过弹簧力和电荷斥力自动排列到最优位置
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// 知识图谱JavaScript初始化

// 全局变量 - 检查是否已存在，避免重复声明
if (typeof allNodes === 'undefined') {
    var allNodes = [];
}
if (typeof allEdges === 'undefined') {
    var allEdges = [];
}
if (typeof filteredNodes === 'undefined') {
    var filteredNodes = [];
}
if (typeof filteredEdges === 'undefined') {
    var filteredEdges = [];
}

// 文本高亮函数
function highlightText(text, query) {
    if (!query || !text) return text;

    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark style="background-color: #FFE066; padding: 1px 2px; border-radius: 2px;">$1</mark>');
}

function refreshGraph() {
    window.location.reload();
}

// 更新图谱统计信息
function updateGraphStats() {
    const nodeCountElement = document.getElementById('nodeCount');
    const edgeCountElement = document.getElementById('edgeCount');

    if (nodeCountElement && edgeCountElement) {
        const nodeCount = allNodes ? allNodes.length : 0;
        const edgeCount = allEdges ? allEdges.length : 0;

        // 只有当有实际数据时才更新
        if (nodeCount > 0 || edgeCount > 0) {
            nodeCountElement.textContent = nodeCount;
            edgeCountElement.textContent = edgeCount;
        }
    }
}

// 检查Neo4j连接状态
function checkConnectionStatus() {
    fetch('/api/knowledge-graph/connection-status', {
        method: 'GET',
        credentials: 'same-origin',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            const statusElement = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');
            const sourceElement = document.getElementById('dataSource');
            const nodeCountElement = document.getElementById('nodeCount');
            const edgeCountElement = document.getElementById('edgeCount');

            if (!statusElement || !textElement || !sourceElement || !nodeCountElement || !edgeCountElement) {
                return;
            }

            if (data.success && data.connected) {
                statusElement.style.backgroundColor = '#28a745';
                textElement.textContent = 'Neo4j已连接';
                sourceElement.textContent = `数据源: ${data.data_source}`;

                if (data.node_count !== undefined && data.edge_count !== undefined) {
                    nodeCountElement.textContent = data.node_count;
                    edgeCountElement.textContent = data.edge_count;
                }
            } else {
                statusElement.style.backgroundColor = '#dc3545';
                textElement.textContent = 'Neo4j连接失败';
                sourceElement.textContent = `数据源: ${data.data_source || 'Neo4j连接失败'}`;
                nodeCountElement.textContent = '0';
                edgeCountElement.textContent = '0';
            }
        })
        .catch(error => {
            console.error('检查连接状态失败:', error);
            const statusElement = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');
            const sourceElement = document.getElementById('dataSource');

            if (statusElement) statusElement.style.backgroundColor = '#dc3545';
            if (textElement) textElement.textContent = 'Neo4j连接检查失败';
            if (sourceElement) sourceElement.textContent = '数据源: 检查失败';
        });
}

// 显示节点详情
function showNodeDetails(nodeData) {
    console.log('showNodeDetails 被调用，节点数据:', nodeData);

    const panel = document.getElementById('nodeDetailsPanel');
    const title = document.getElementById('nodeDetailsTitle');
    const content = document.getElementById('nodeDetailsContent');

    console.log('面板元素:', panel, '标题元素:', title, '内容元素:', content);

    if (!nodeData) {
        console.log('节点数据为空，退出');
        return;
    }

    if (!panel || !title || !content) {
        console.log('面板元素未找到');
        return;
    }

    // 设置标题 - 优先使用label，然后是其他字段
    const nodeTitle = nodeData.label || nodeData.name || nodeData.title || '未知节点';
    title.textContent = nodeTitle;

    // 构建详情内容
    let detailsHTML = '';

    // 基本信息
    detailsHTML += `
        <div class="node-property">
            <div class="property-label">节点类型</div>
            <div class="property-value">${nodeData.group || nodeData.type || '未知'}</div>
        </div>
        <div class="node-property">
            <div class="property-label">节点ID</div>
            <div class="property-value">${nodeData.id || '未知'}</div>
        </div>
    `;

    // 如果有显示名称，添加到详情中
    if (nodeData.label && nodeData.label !== nodeTitle) {
        detailsHTML += `
            <div class="node-property">
                <div class="property-label">显示名称</div>
                <div class="property-value">${nodeData.label}</div>
            </div>
        `;
    }

    // 从title中解析更多信息（PyVis的title字段包含悬停信息）
    if (nodeData.title) {
        const titleParts = nodeData.title.split('<br>');
        titleParts.forEach(part => {
            if (part.includes(':') && !part.includes('类型:') && !part.includes('名称:')) {
                const [key, value] = part.split(':');
                if (key && value) {
                    detailsHTML += `
                        <div class="node-property">
                            <div class="property-label">${key.trim()}</div>
                            <div class="property-value">${value.trim()}</div>
                        </div>
                    `;
                }
            }
        });
    }

    // 如果有properties字段（来自Neo4j的原始数据），显示其中的信息
    if (nodeData.properties && typeof nodeData.properties === 'object') {
        Object.entries(nodeData.properties).forEach(([key, value]) => {
            if (key !== 'name' && key !== 'title' && value) {
                detailsHTML += `
                    <div class="node-property">
                        <div class="property-label">${key}</div>
                        <div class="property-value">${value}</div>
                    </div>
                `;
            }
        });
    }

    // 连接信息
    if (window.knowledgeGraphNetwork && window.knowledgeGraphEdges) {
        try {
            const connectedEdges = window.knowledgeGraphNetwork.getConnectedEdges(nodeData.id);
            if (connectedEdges && connectedEdges.length > 0) {
                detailsHTML += `
                    <div class="connections-section">
                        <div class="connections-title">连接关系 (${connectedEdges.length})</div>
                `;

                connectedEdges.slice(0, 10).forEach(edgeId => {
                    const edge = window.knowledgeGraphEdges.get(edgeId);
                    if (edge) {
                        const otherNodeId = edge.from === nodeData.id ? edge.to : edge.from;
                        const otherNode = window.knowledgeGraphNodes.get(otherNodeId);
                        const relationshipType = edge.label || '未知关系';
                        const otherNodeName = otherNode ? (otherNode.label || otherNode.name || '未知节点') : '未知节点';

                        detailsHTML += `
                            <div class="connection-item">
                                <strong>${relationshipType}</strong> → ${otherNodeName}
                            </div>
                        `;
                    }
                });

                if (connectedEdges.length > 10) {
                    detailsHTML += `<div class="connection-item">... 还有 ${connectedEdges.length - 10} 个连接</div>`;
                }

                detailsHTML += '</div>';
            } else {
                detailsHTML += `
                    <div class="connections-section">
                        <div class="connections-title">连接关系</div>
                        <div class="connection-item">暂无连接关系</div>
                    </div>
                `;
            }
        } catch (e) {
            console.log('获取连接信息时出错:', e);
            detailsHTML += `
                <div class="connections-section">
                    <div class="connections-title">连接关系</div>
                    <div class="connection-item">获取连接信息失败</div>
                </div>
            `;
        }
    }

    content.innerHTML = detailsHTML;

    // 使用动画显示面板
    panel.style.display = 'block';
    panel.style.visibility = 'visible';

    // 强制重绘
    panel.offsetHeight;

    setTimeout(() => {
        panel.classList.add('show');
    }, 10);

    console.log('节点详情面板应该已显示');
}

// 隐藏节点详情
function hideNodeDetails() {
    const panel = document.getElementById('nodeDetailsPanel');
    panel.classList.remove('show');
    setTimeout(() => {
        panel.style.display = 'none';
    }, 300);
}

// 搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('nodeSearchInput');
    const searchResults = document.getElementById('searchResults');

    if (!searchInput) {
        console.log('搜索输入框未找到');
        return;
    }

    // 收集所有节点数据用于搜索
    if (window.knowledgeGraphNodes) {
        allNodes = window.knowledgeGraphNodes.get();
        console.log('搜索功能初始化完成，节点数量:', allNodes.length);
    } else {
        console.log('节点数据未找到，搜索功能暂时不可用');
        allNodes = [];
    }

    searchInput.addEventListener('input', function() {
        const query = this.value.trim().toLowerCase();

        if (query.length < 2) {
            searchResults.style.display = 'none';
            return;
        }

        // 搜索匹配的节点
        const matches = allNodes.filter(node => {
            const name = (node.label || '').toLowerCase();
            const type = (node.group || '').toLowerCase();
            const title = (node.title || '').toLowerCase();

            return name.includes(query) || type.includes(query) || title.includes(query);
        }).slice(0, 10); // 限制结果数量

        if (matches.length > 0) {
            let resultsHTML = '';
            matches.forEach(node => {
                const name = node.label || '未知节点';
                const type = node.group || '未知类型';

                resultsHTML += `
                    <div class="search-result-item" onclick="selectSearchResult('${node.id}')">
                        <strong>${name}</strong><br>
                        <small class="text-muted">${type}</small>
                    </div>
                `;
            });
            searchResults.innerHTML = resultsHTML;
            searchResults.style.display = 'block';
        } else {
            searchResults.innerHTML = '<div class="search-result-item">未找到匹配的节点</div>';
            searchResults.style.display = 'block';
        }
    });

    // 点击外部隐藏搜索结果
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.style.display = 'none';
        }
    });
}

// 选择搜索结果
function selectSearchResult(nodeId) {
    const searchResults = document.getElementById('searchResults');
    searchResults.style.display = 'none';

    if (window.knowledgeGraphNetwork && window.knowledgeGraphNodes) {
        // 高亮选中的节点
        window.knowledgeGraphNetwork.selectNodes([nodeId]);

        // 移动视图到选中的节点
        window.knowledgeGraphNetwork.focus(nodeId, {
            scale: 1.5,
            animation: {
                duration: 1000,
                easingFunction: 'easeInOutQuad'
            }
        });

        // 显示节点详情
        const nodeData = window.knowledgeGraphNodes.get(nodeId);
        if (nodeData) {
            showNodeDetails(nodeData);
        }
    }
}

// 高级筛选功能
function toggleAdvancedFilters() {
    const advancedFilters = document.getElementById('advancedFilters');
    const toggleText = document.getElementById('advancedToggleText');

    if (!advancedFilters || !toggleText) {
        console.log('高级筛选元素未找到');
        return;
    }

    if (advancedFilters.style.display === 'none' || advancedFilters.style.display === '') {
        advancedFilters.style.display = 'block';
        toggleText.textContent = '隐藏高级筛选';
        console.log('显示高级筛选面板');
    } else {
        advancedFilters.style.display = 'none';
        toggleText.textContent = '显示高级筛选';
        console.log('隐藏高级筛选面板');
    }
}

function applyAdvancedFilters() {
    console.log('应用高级筛选...');

    if (!window.knowledgeGraphNetwork || !window.knowledgeGraphNodes || !window.knowledgeGraphEdges) {
        console.log('图谱对象未找到，无法应用筛选');
        alert('图谱尚未加载完成，请稍后再试');
        return;
    }

    // 获取选中的节点类型
    const selectedNodeTypes = Array.from(document.querySelectorAll('.node-type-filter:checked'))
        .map(cb => cb.value);
    console.log('选中的节点类型:', selectedNodeTypes);

    // 获取选中的关系类型
    const selectedRelationshipTypes = Array.from(document.querySelectorAll('.relationship-type-filter:checked'))
        .map(cb => cb.value);
    console.log('选中的关系类型:', selectedRelationshipTypes);

    // 筛选节点
    let nodesToShow = allNodes;
    if (selectedNodeTypes.length > 0) {
        nodesToShow = allNodes.filter(node =>
            selectedNodeTypes.includes(node.group)
        );
    }

    // 筛选边
    let edgesToShow = allEdges;
    if (selectedRelationshipTypes.length > 0) {
        edgesToShow = allEdges.filter(edge =>
            selectedRelationshipTypes.includes(edge.label)
        );
    }

    // 确保边的两端节点都存在
    const nodeIds = new Set(nodesToShow.map(node => node.id));
    edgesToShow = edgesToShow.filter(edge =>
        nodeIds.has(edge.from) && nodeIds.has(edge.to)
    );

    // 更新图谱
    window.knowledgeGraphNodes.clear();
    window.knowledgeGraphEdges.clear();
    window.knowledgeGraphNodes.add(nodesToShow);
    window.knowledgeGraphEdges.add(edgesToShow);

    // 更新统计信息
    const nodeCountElement = document.getElementById('nodeCount');
    const edgeCountElement = document.getElementById('edgeCount');
    if (nodeCountElement && edgeCountElement) {
        nodeCountElement.textContent = nodesToShow.length;
        edgeCountElement.textContent = edgesToShow.length;
    }

    // 重新稳定化布局
    window.knowledgeGraphNetwork.stabilize();

    console.log('高级筛选完成 - 显示节点:', nodesToShow.length, '显示边:', edgesToShow.length);
}

function clearAdvancedFilters() {
    console.log('清除高级筛选');

    // 清除所有复选框
    document.querySelectorAll('.node-type-filter, .relationship-type-filter').forEach(cb => {
        cb.checked = false;
    });

    // 恢复所有节点和边
    if (window.knowledgeGraphNodes && window.knowledgeGraphEdges) {
        window.knowledgeGraphNodes.clear();
        window.knowledgeGraphEdges.clear();
        window.knowledgeGraphNodes.add(allNodes);
        window.knowledgeGraphEdges.add(allEdges);

        // 更新统计信息
        updateGraphStats();

        window.knowledgeGraphNetwork.stabilize();
        console.log('高级筛选已清除，恢复所有数据');
    } else {
        console.log('无法清除筛选，缺少必要的对象');
    }
}

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // ESC键关闭节点详情面板
    if (e.key === 'Escape') {
        hideNodeDetails();
        // 也隐藏搜索结果
        const searchResults = document.getElementById('searchResults');
        if (searchResults) {
            searchResults.style.display = 'none';
        }
    }

    // Ctrl+F 或 Cmd+F 聚焦搜索框
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        const searchInput = document.getElementById('nodeSearchInput');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 [DEBUG] DOM加载完成，开始初始化...');

    // 检查图谱容器是否存在
    const graphContainer = document.getElementById('pyvis-graph');
    console.log('🔍 [DEBUG] 图谱容器:', graphContainer);
    if (graphContainer) {
        console.log('🔍 [DEBUG] 图谱容器内容:', graphContainer.innerHTML.substring(0, 200) + '...');
        console.log('🔍 [DEBUG] 图谱容器尺寸:', {
            width: graphContainer.offsetWidth,
            height: graphContainer.offsetHeight,
            display: window.getComputedStyle(graphContainer).display,
            visibility: window.getComputedStyle(graphContainer).visibility
        });
    }

    // 检查vis-network库是否加载
    console.log('🔍 [DEBUG] vis对象是否存在:', typeof vis !== 'undefined');

    // 初始化筛选表单
    initializeFilterForm();

    // 检查Neo4j连接状态
    checkConnectionStatus();

    // 等待PyVis图谱加载完成
    setTimeout(() => {
        console.log('🔍 [DEBUG] 2秒后检查PyVis状态...');
        console.log('🔍 [DEBUG] window.knowledgeGraphNetwork:', window.knowledgeGraphNetwork);
        console.log('🔍 [DEBUG] window.knowledgeGraphNodes:', window.knowledgeGraphNodes);
        console.log('🔍 [DEBUG] window.knowledgeGraphEdges:', window.knowledgeGraphEdges);

        // 检查mynetwork容器
        const mynetwork = document.getElementById('mynetwork');
        console.log('🔍 [DEBUG] mynetwork容器:', mynetwork);
        if (mynetwork) {
            console.log('🔍 [DEBUG] mynetwork尺寸:', {
                width: mynetwork.offsetWidth,
                height: mynetwork.offsetHeight,
                display: window.getComputedStyle(mynetwork).display
            });
            console.log('🔍 [DEBUG] mynetwork子元素数量:', mynetwork.children.length);
        }

        initializeSearch();
        initializeGraphEvents();

        // 保存原始数据
        if (window.knowledgeGraphNodes && window.knowledgeGraphEdges) {
            try {
                allNodes = window.knowledgeGraphNodes.get();
                allEdges = window.knowledgeGraphEdges.get();
                console.log('🔍 [DEBUG] 成功获取数据 - 节点:', allNodes.length, '边:', allEdges.length);
                updateGraphStats();
            } catch (e) {
                console.error('🔍 [DEBUG] 获取PyVis数据失败:', e);
                // 如果获取失败，稍后重试
                setTimeout(() => {
                    try {
                        allNodes = window.knowledgeGraphNodes.get();
                        allEdges = window.knowledgeGraphEdges.get();
                        console.log('🔍 [DEBUG] 重试成功获取数据 - 节点:', allNodes.length, '边:', allEdges.length);
                        updateGraphStats();
                    } catch (e) {
                        console.error('🔍 [DEBUG] 重试获取PyVis数据仍然失败:', e);
                    }
                }, 1000);
            }
        } else {
            console.error('🔍 [DEBUG] PyVis对象不存在，无法获取数据');
        }
    }, 2000);

});

// 初始化筛选表单
function initializeFilterForm() {
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        // 移除现有的事件监听器，避免重复绑定
        const newForm = filterForm.cloneNode(true);
        filterForm.parentNode.replaceChild(newForm, filterForm);

        newForm.addEventListener('submit', function(e) {
            e.preventDefault(); // 阻止表单默认提交
            applyFilters(); // 使用AJAX更新
        });

        // 为筛选控件添加实时更新
        const nodeTypeSelect = newForm.querySelector('select[name="node_type"]');
        const relationshipTypeSelect = newForm.querySelector('select[name="relationship_type"]');
        const limitInput = newForm.querySelector('input[name="limit"]');

        if (nodeTypeSelect) {
            nodeTypeSelect.addEventListener('change', debounce(applyFilters, 300));
        }
        if (relationshipTypeSelect) {
            relationshipTypeSelect.addEventListener('change', debounce(applyFilters, 300));
        }
        if (limitInput) {
            limitInput.addEventListener('change', debounce(applyFilters, 500));
        }
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 防止重复提交的标志
let isApplyingFilters = false;

// 应用筛选器
function applyFilters() {
    // 防止重复提交
    if (isApplyingFilters) {
        return;
    }

    const filterForm = document.getElementById('filterForm');
    if (!filterForm) return;

    isApplyingFilters = true;

    const formData = new FormData(filterForm);
    const nodeType = formData.get('node_type') || '';
    const relationshipType = formData.get('relationship_type') || '';
    const limit = formData.get('limit') || 100;

    // 显示加载状态
    const graphContainer = document.getElementById('pyvis-graph');
    if (graphContainer) {
        graphContainer.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i><br>正在更新图谱...</div>';
    }

    // 构建API请求参数
    const params = new URLSearchParams();
    if (nodeType) params.append('node_type', nodeType);
    if (relationshipType) params.append('relationship_type', relationshipType);
    if (limit) params.append('limit', limit);

    // 发送AJAX请求
    fetch(`/api/knowledge-graph/data?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新图谱 - 使用更安全的方式
                if (graphContainer && data.graph_html) {
                    // 清空当前容器
                    graphContainer.innerHTML = '';

                    // 创建新的div来包含PyVis内容
                    const newGraphDiv = document.createElement('div');
                    newGraphDiv.innerHTML = data.graph_html;

                    // 将新内容添加到容器中
                    graphContainer.appendChild(newGraphDiv);

                    // 重新初始化图谱事件（延迟执行，确保PyVis完全加载）
                    setTimeout(() => {
                        initializeGraphEvents();
                        initializeSearch();

                        // 重新保存数据（多次尝试，确保数据加载成功）
                        let retryCount = 0;
                        const maxRetries = 5;

                        function tryGetData() {
                            if (window.knowledgeGraphNodes && window.knowledgeGraphEdges) {
                                try {
                                    allNodes = window.knowledgeGraphNodes.get();
                                    allEdges = window.knowledgeGraphEdges.get();
                                    if (allNodes.length > 0 || allEdges.length > 0) {
                                        updateGraphStats();
                                        return;
                                    }
                                } catch (e) {
                                    console.error('获取数据失败:', e);
                                }
                            }

                            retryCount++;
                            if (retryCount < maxRetries) {
                                setTimeout(tryGetData, 500);
                            }
                        }

                        tryGetData();
                    }, 1500);
                }

                // 更新统计信息
                if (data.stats) {
                    const nodeCountElement = document.getElementById('nodeCount');
                    const edgeCountElement = document.getElementById('edgeCount');

                    if (nodeCountElement) {
                        nodeCountElement.textContent = data.stats.node_count;
                    }
                    if (edgeCountElement) {
                        edgeCountElement.textContent = data.stats.edge_count;
                    }
                }
            } else {
                console.error('图谱更新失败:', data.error);
                if (graphContainer) {
                    graphContainer.innerHTML = `<div class="alert alert-danger">更新图谱失败: ${data.error}</div>`;
                }
            }
        })
        .catch(error => {
            console.error('AJAX请求失败:', error);
            if (graphContainer) {
                graphContainer.innerHTML = '<div class="alert alert-danger">网络请求失败，请重试</div>';
            }
        })
        .finally(() => {
            // 重置提交标志
            isApplyingFilters = false;
        });
}

// 清除筛选器
function clearFilters() {
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        // 重置表单
        filterForm.reset();

        // 应用空筛选（显示所有数据）
        applyFilters();
    }
}

// 初始化图谱事件
function initializeGraphEvents() {
    let network = null;
    let retryCount = 0;
    const maxRetries = 10;

    function findNetwork() {
        // 方法1：从全局变量获取
        if (window.network) {
            network = window.network;
        }
        // 方法2：从PyVis生成的变量获取
        else if (window.mynetworkid) {
            network = window.mynetworkid;
        }
        // 方法3：查找所有可能的网络对象
        else {
            const canvasElements = document.querySelectorAll('#pyvis-graph canvas, #mynetwork canvas');
            if (canvasElements.length > 0) {
                // 遍历全局变量寻找网络对象
                for (let key in window) {
                    if (window[key] && typeof window[key] === 'object' &&
                        window[key].body && window[key].body.nodes) {
                        network = window[key];
                        break;
                    }
                }
            }
        }

        if (network) {
            // 设置全局变量
            window.knowledgeGraphNetwork = network;

            // 获取节点和边数据
            if (network.body && network.body.data) {
                window.knowledgeGraphNodes = network.body.data.nodes;
                window.knowledgeGraphEdges = network.body.data.edges;
            }

            return true;
        } else {
            retryCount++;
            if (retryCount < maxRetries) {
                setTimeout(findNetwork, 500);
            }
            return false;
        }
    }

    findNetwork();
}
</script>
{% endblock %}
