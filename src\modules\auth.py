"""
用户认证蓝图
处理用户注册、登录、登出等认证相关功能
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from .models import User
from .database import DatabaseManager
import logging
import re

logger = logging.getLogger(__name__)

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

# 数据库管理器实例（需要在应用初始化时设置）
db_manager = None


def init_auth(database_manager: DatabaseManager):
    """初始化认证模块"""
    global db_manager
    db_manager = database_manager


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_username(username: str) -> bool:
    """验证用户名格式"""
    # 用户名长度3-20，只能包含字母、数字、下划线
    pattern = r'^[a-zA-Z0-9_]{3,20}$'
    return re.match(pattern, username) is not None


def validate_password(password: str) -> bool:
    """验证密码强度"""
    # 密码长度至少6位
    return len(password) >= 6


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'GET':
        # 如果已登录，根据角色重定向
        if current_user.is_authenticated:
            if current_user.is_admin():
                return redirect(url_for('admin_index'))  # 管理员后台
            else:
                return redirect(url_for('quiz.home'))  # 用户刷题界面
        
        return render_template('auth/login.html')
    
    # POST请求处理登录
    username = request.form.get('username', '').strip()
    password = request.form.get('password', '')
    remember = request.form.get('remember', False)
    
    if not username or not password:
        flash('请输入用户名和密码', 'error')
        return render_template('auth/login.html')
    
    # 查找用户
    user = User.get_by_username(username, db_manager)
    if not user or not user.check_password(password):
        flash('用户名或密码错误', 'error')
        return render_template('auth/login.html')
    
    # 登录成功
    login_user(user, remember=remember)
    user.update_last_login(db_manager)
    
    logger.info(f"用户 {username} 登录成功，角色: {user.role}")
    
    # 根据用户角色重定向
    next_page = request.args.get('next')
    if next_page:
        return redirect(next_page)
    elif user.is_admin():
        return redirect(url_for('admin_index'))  # 管理员后台
    else:
        return redirect(url_for('quiz.home'))  # 用户刷题界面


@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'GET':
        return render_template('auth/register.html')
    
    # POST请求处理注册
    username = request.form.get('username', '').strip()
    email = request.form.get('email', '').strip()
    password = request.form.get('password', '')
    confirm_password = request.form.get('confirm_password', '')
    
    # 验证输入
    errors = []
    
    if not username:
        errors.append('请输入用户名')
    elif not validate_username(username):
        errors.append('用户名格式不正确（3-20位字母、数字、下划线）')
    
    if not email:
        errors.append('请输入邮箱')
    elif not validate_email(email):
        errors.append('邮箱格式不正确')
    
    if not password:
        errors.append('请输入密码')
    elif not validate_password(password):
        errors.append('密码长度至少6位')
    
    if password != confirm_password:
        errors.append('两次输入的密码不一致')
    
    if errors:
        for error in errors:
            flash(error, 'error')
        return render_template('auth/register.html')
    
    # 检查用户名和邮箱是否已存在
    existing_user = User.get_by_username(username, db_manager)
    if existing_user:
        flash('用户名已存在', 'error')
        return render_template('auth/register.html')
    
    # 创建新用户
    user = User.create(username=username, email=email, password=password, 
                      role='user', db_manager=db_manager)
    
    if user:
        flash('注册成功，请登录', 'success')
        logger.info(f"新用户注册成功: {username}")
        return redirect(url_for('auth.login'))
    else:
        flash('注册失败，请稍后重试', 'error')
        return render_template('auth/register.html')


@auth_bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    username = current_user.username
    logout_user()
    logger.info(f"用户 {username} 已登出")
    flash('已成功登出', 'info')
    return redirect(url_for('auth.login'))


@auth_bp.route('/api/check-username')
def check_username():
    """检查用户名是否可用（AJAX接口）"""
    username = request.args.get('username', '').strip()
    
    if not username:
        return jsonify({'available': False, 'message': '用户名不能为空'})
    
    if not validate_username(username):
        return jsonify({'available': False, 'message': '用户名格式不正确'})
    
    existing_user = User.get_by_username(username, db_manager)
    if existing_user:
        return jsonify({'available': False, 'message': '用户名已存在'})
    
    return jsonify({'available': True, 'message': '用户名可用'})


@auth_bp.route('/api/check-email')
def check_email():
    """检查邮箱是否可用（AJAX接口）"""
    email = request.args.get('email', '').strip()
    
    if not email:
        return jsonify({'available': False, 'message': '邮箱不能为空'})
    
    if not validate_email(email):
        return jsonify({'available': False, 'message': '邮箱格式不正确'})
    
    # 这里需要实现邮箱唯一性检查
    # 由于当前模型中没有按邮箱查找的方法，暂时返回可用
    return jsonify({'available': True, 'message': '邮箱可用'})


# 错误处理
@auth_bp.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return render_template('auth/404.html'), 404


@auth_bp.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"认证模块内部错误: {error}")
    return render_template('auth/500.html'), 500
