"""
数据模型定义
包含用户系统和答题功能的所有数据模型
"""

from flask_login import UserMixin
from datetime import datetime
from typing import Optional, List, Dict, Any
import bcrypt
import pymysql
from .database import DatabaseManager
import logging

logger = logging.getLogger(__name__)


class User(UserMixin):
    """用户模型"""
    
    def __init__(self, user_id: int, username: str, email: str, 
                 password_hash: str, role: str = 'user', 
                 created_at: datetime = None, last_login: datetime = None):
        self.user_id = user_id
        self.username = username
        self.email = email
        self.password_hash = password_hash
        self.role = role
        self.created_at = created_at
        self.last_login = last_login
    
    def get_id(self):
        """Flask-Login要求的方法"""
        return str(self.user_id)
    
    def is_admin(self) -> bool:
        """检查是否为管理员"""
        return self.role == 'admin'
    
    def check_password(self, password: str) -> bool:
        """验证密码"""
        try:
            # 先尝试 bcrypt 验证
            if self.password_hash.startswith('$2b$'):
                return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
            else:
                # 使用简单哈希验证（临时方案）
                import hashlib
                return hashlib.sha256(password.encode()).hexdigest() == self.password_hash
        except Exception as e:
            logger.error(f"密码验证失败: {e}")
            # 尝试简单哈希验证
            try:
                import hashlib
                return hashlib.sha256(password.encode()).hexdigest() == self.password_hash
            except:
                return False

    @staticmethod
    def hash_password(password: str) -> str:
        """生成密码哈希"""
        try:
            salt = bcrypt.gensalt()
            return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        except Exception as e:
            logger.error(f"bcrypt 哈希失败，使用简单哈希: {e}")
            # 使用简单哈希作为备选方案
            import hashlib
            return hashlib.sha256(password.encode()).hexdigest()
    
    @classmethod
    def get_by_id(cls, user_id: int, db_manager: DatabaseManager) -> Optional['User']:
        """根据用户ID获取用户"""
        connection = db_manager.get_connection()
        if not connection:
            return None
        
        try:
            cursor = connection.cursor()
            cursor.execute("""
                SELECT user_id, username, email, password_hash, role, created_at, last_login
                FROM users WHERE user_id = %s
            """, (user_id,))
            
            row = cursor.fetchone()
            if row:
                return cls(**row)
            return None
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
        finally:
            connection.close()
    
    @classmethod
    def get_by_username(cls, username: str, db_manager: DatabaseManager) -> Optional['User']:
        """根据用户名获取用户"""
        connection = db_manager.get_connection()
        if not connection:
            return None
        
        try:
            cursor = connection.cursor()
            cursor.execute("""
                SELECT user_id, username, email, password_hash, role, created_at, last_login
                FROM users WHERE username = %s
            """, (username,))
            
            row = cursor.fetchone()
            if row:
                return cls(**row)
            return None
        except Exception as e:
            logger.error(f"获取用户失败: {e}")
            return None
        finally:
            connection.close()
    
    @classmethod
    def create(cls, username: str, email: str, password: str, 
               role: str = 'user', db_manager: DatabaseManager = None) -> Optional['User']:
        """创建新用户"""
        if not db_manager:
            return None
            
        connection = db_manager.get_connection()
        if not connection:
            return None
        
        try:
            cursor = connection.cursor()
            password_hash = cls.hash_password(password)
            
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, role)
                VALUES (%s, %s, %s, %s)
            """, (username, email, password_hash, role))
            
            user_id = cursor.lastrowid
            connection.commit()
            
            return cls(user_id=user_id, username=username, email=email,
                      password_hash=password_hash, role=role, created_at=datetime.now())
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            connection.rollback()
            return None
        finally:
            connection.close()
    
    def update_last_login(self, db_manager: DatabaseManager) -> bool:
        """更新最后登录时间"""
        connection = db_manager.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            cursor.execute("""
                UPDATE users SET last_login = CURRENT_TIMESTAMP
                WHERE user_id = %s
            """, (self.user_id,))
            
            connection.commit()
            self.last_login = datetime.now()
            return True
        except Exception as e:
            logger.error(f"更新最后登录时间失败: {e}")
            connection.rollback()
            return False
        finally:
            connection.close()


class UserAnswer:
    """用户答题记录模型"""
    
    def __init__(self, answer_id: int = None, user_id: int = None, 
                 question_id: int = None, user_answer: str = None,
                 is_correct: bool = False, time_spent: int = 0,
                 answered_at: datetime = None):
        self.answer_id = answer_id
        self.user_id = user_id
        self.question_id = question_id
        self.user_answer = user_answer
        self.is_correct = is_correct
        self.time_spent = time_spent
        self.answered_at = answered_at
    
    @classmethod
    def create(cls, user_id: int, question_id: int, user_answer: str,
               is_correct: bool, time_spent: int = 0, 
               db_manager: DatabaseManager = None) -> Optional['UserAnswer']:
        """创建答题记录"""
        if not db_manager:
            return None
            
        connection = db_manager.get_connection()
        if not connection:
            return None
        
        try:
            cursor = connection.cursor()
            cursor.execute("""
                INSERT INTO user_answers (user_id, question_id, user_answer, is_correct, time_spent)
                VALUES (%s, %s, %s, %s, %s)
            """, (user_id, question_id, user_answer, is_correct, time_spent))
            
            answer_id = cursor.lastrowid
            connection.commit()
            
            return cls(answer_id=answer_id, user_id=user_id, question_id=question_id,
                      user_answer=user_answer, is_correct=is_correct, 
                      time_spent=time_spent, answered_at=datetime.now())
        except Exception as e:
            logger.error(f"创建答题记录失败: {e}")
            connection.rollback()
            return None
        finally:
            connection.close()
    
    @classmethod
    def get_user_history(cls, user_id: int, page: int = 1, per_page: int = 20,
                        db_manager: DatabaseManager = None) -> List['UserAnswer']:
        """获取用户答题历史"""
        if not db_manager:
            return []
            
        connection = db_manager.get_connection()
        if not connection:
            return []
        
        try:
            cursor = connection.cursor()
            offset = (page - 1) * per_page
            
            cursor.execute("""
                SELECT ua.answer_id, ua.user_id, ua.question_id, ua.user_answer,
                       ua.is_correct, ua.time_spent, ua.answered_at,
                       q.question_text
                FROM user_answers ua
                JOIN questions q ON ua.question_id = q.id
                WHERE ua.user_id = %s
                ORDER BY ua.answered_at DESC
                LIMIT %s OFFSET %s
            """, (user_id, per_page, offset))
            
            results = []
            for row in cursor.fetchall():
                answer = cls(
                    answer_id=row['answer_id'],
                    user_id=row['user_id'],
                    question_id=row['question_id'],
                    user_answer=row['user_answer'],
                    is_correct=row['is_correct'],
                    time_spent=row['time_spent'],
                    answered_at=row['answered_at']
                )
                answer.question_text = row['question_text']  # 额外信息
                results.append(answer)
            
            return results
        except Exception as e:
            logger.error(f"获取用户答题历史失败: {e}")
            return []
        finally:
            connection.close()


class QuestionStats:
    """题目统计模型"""
    
    def __init__(self, question_id: int, total_attempts: int = 0, correct_attempts: int = 0):
        self.question_id = question_id
        self.total_attempts = total_attempts
        self.correct_attempts = correct_attempts
    
    @property
    def accuracy_rate(self) -> float:
        """正确率"""
        if self.total_attempts == 0:
            return 0.0
        return self.correct_attempts / self.total_attempts
    
    @classmethod
    def update_stats(cls, question_id: int, is_correct: bool, 
                    db_manager: DatabaseManager = None) -> bool:
        """更新题目统计"""
        if not db_manager:
            return False
            
        connection = db_manager.get_connection()
        if not connection:
            return False
        
        try:
            cursor = connection.cursor()
            
            # 使用INSERT ... ON DUPLICATE KEY UPDATE语法
            cursor.execute("""
                INSERT INTO question_stats (question_id, total_attempts, correct_attempts)
                VALUES (%s, 1, %s)
                ON DUPLICATE KEY UPDATE
                total_attempts = total_attempts + 1,
                correct_attempts = correct_attempts + %s
            """, (question_id, 1 if is_correct else 0, 1 if is_correct else 0))
            
            connection.commit()
            return True
        except Exception as e:
            logger.error(f"更新题目统计失败: {e}")
            connection.rollback()
            return False
        finally:
            connection.close()


class UserFavorite:
    """用户收藏模型"""
    
    def __init__(self, favorite_id: int = None, user_id: int = None,
                 question_id: int = None, created_at: datetime = None):
        self.favorite_id = favorite_id
        self.user_id = user_id
        self.question_id = question_id
        self.created_at = created_at
    
    @classmethod
    def toggle_favorite(cls, user_id: int, question_id: int,
                       db_manager: DatabaseManager = None) -> Dict[str, Any]:
        """切换收藏状态"""
        if not db_manager:
            return {'success': False, 'message': '数据库连接失败'}
            
        connection = db_manager.get_connection()
        if not connection:
            return {'success': False, 'message': '数据库连接失败'}
        
        try:
            cursor = connection.cursor()
            
            # 检查是否已收藏
            cursor.execute("""
                SELECT favorite_id FROM user_favorites
                WHERE user_id = %s AND question_id = %s
            """, (user_id, question_id))
            
            existing = cursor.fetchone()
            
            if existing:
                # 取消收藏
                cursor.execute("""
                    DELETE FROM user_favorites
                    WHERE user_id = %s AND question_id = %s
                """, (user_id, question_id))
                action = 'removed'
            else:
                # 添加收藏
                cursor.execute("""
                    INSERT INTO user_favorites (user_id, question_id)
                    VALUES (%s, %s)
                """, (user_id, question_id))
                action = 'added'
            
            connection.commit()
            return {'success': True, 'action': action}
        except Exception as e:
            logger.error(f"切换收藏状态失败: {e}")
            connection.rollback()
            return {'success': False, 'message': str(e)}
        finally:
            connection.close()
