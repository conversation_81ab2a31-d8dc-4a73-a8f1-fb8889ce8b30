{% extends "base.html" %}

{% block title %}文档预处理 - 地理题库管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-file-word"></i> 文档预处理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-success" onclick="startProcessing()">
            <i class="fas fa-play"></i> 开始处理
        </button>
    </div>
</div>

<!-- 配置信息卡片 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-cog"></i> 当前配置
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-6">
                        <strong>API密钥状态:</strong>
                    </div>
                    <div class="col-sm-6">
                        {% if config.api.deepseek_api_key != 'your_api_key_here' %}
                            <span class="badge bg-success">已配置</span>
                        {% else %}
                            <span class="badge bg-danger">未配置</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-sm-6">
                        <strong>使用缓存:</strong>
                    </div>
                    <div class="col-sm-6">
                        {% if config.use_cache %}
                            <span class="badge bg-success">启用</span>
                        {% else %}
                            <span class="badge bg-secondary">禁用</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-sm-6">
                        <strong>待处理文件:</strong>
                    </div>
                    <div class="col-sm-6">
                        <span class="badge bg-info">{{ config.files|length }} 个</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-upload"></i> 文件上传
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="docxFile" class="form-label">选择Word文档 (.docx)</label>
                    <input class="form-control" type="file" id="docxFile" accept=".docx" multiple>
                    <div class="form-text">支持批量上传多个.docx文件</div>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="overwriteFiles" checked>
                        <label class="form-check-label" for="overwriteFiles">
                            覆盖已存在的文件
                        </label>
                        <div class="form-text">如果取消勾选，将跳过已存在的文件</div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary" onclick="uploadFiles()">
                    <i class="fas fa-upload"></i> 上传文件
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 文件列表 -->
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-list"></i> 待处理文件列表
        <button class="btn btn-sm btn-outline-primary float-end" onclick="refreshFileList()">
            <i class="fas fa-sync-alt"></i> 刷新
        </button>
    </div>
    <div class="card-body">
        {% if config.files %}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="file-list">
                        {% for file in config.files %}
                        <tr>
                            <td>
                                <i class="fas fa-file-word text-primary"></i>
                                {{ file }}
                            </td>
                            <td>
                                <span class="badge bg-warning">待处理</span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-danger" onclick="removeFile('{{ file }}')">
                                    <i class="fas fa-trash"></i> 移除
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center text-muted py-4">
                <i class="fas fa-folder-open fa-3x mb-3"></i>
                <p>暂无待处理文件，请上传Word文档</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 下载结果 -->
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-download"></i> 下载分析结果
        <button class="btn btn-sm btn-outline-primary float-end" onclick="refreshDownloadList()">
            <i class="fas fa-sync-alt"></i> 刷新
        </button>
    </div>
    <div class="card-body">
        <div id="download-list-container">
            <div class="text-center text-muted py-4">
                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                <p>正在加载下载列表...</p>
            </div>
        </div>
    </div>
</div>

<!-- 处理进度 -->
<div class="card mb-4" id="progress-card" style="display: none;">
    <div class="card-header">
        <i class="fas fa-tasks"></i> 处理进度
    </div>
    <div class="card-body">
        <div class="progress mb-3">
            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                 role="progressbar" 
                 style="width: 0%" 
                 id="progress-bar">0%</div>
        </div>
        <div id="current-task" class="text-muted">等待开始...</div>
    </div>
</div>

<!-- 缓存选择卡片 -->
<div class="card mb-4" id="cache-choice-card" style="display: none;">
    <div class="card-header">
        <i class="fas fa-database"></i> 发现缓存文件
        <button class="btn btn-sm btn-outline-secondary float-end" onclick="hideCacheChoice()">
            <i class="fas fa-times"></i> 关闭
        </button>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>发现缓存：</strong>系统检测到该文档已有分析缓存，请选择处理方式。
        </div>

        <div class="mb-3">
            <h6><i class="fas fa-chart-bar"></i> 缓存信息摘要：</h6>
            <div id="cache-summary" class="bg-light p-3 rounded">
                <!-- 缓存摘要信息将在这里显示 -->
            </div>
        </div>

        <div class="d-flex gap-2 flex-wrap justify-content-center">
            <button type="button" class="btn btn-success" onclick="handleCacheChoice('use_cache')">
                <i class="fas fa-check"></i> 使用缓存
            </button>
            <button type="button" class="btn btn-warning" onclick="handleCacheChoice('reanalyze')">
                <i class="fas fa-redo"></i> 重新分析
            </button>
            <button type="button" class="btn btn-info" onclick="handleCacheChoice('edit_cache')">
                <i class="fas fa-edit"></i> 编辑缓存
            </button>
        </div>
    </div>
</div>

<!-- JSON分析数据提交 -->
<div class="card mb-4" id="json-submit-card" style="display: none;">
    <div class="card-header">
        <i class="fas fa-code"></i> 文档分析数据提交
        <button class="btn btn-sm btn-outline-secondary float-end" onclick="hideJsonSubmit()">
            <i class="fas fa-times"></i> 关闭
        </button>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>操作说明：</strong>
            <ol class="mb-0 mt-2">
                <li>将文档上传到ChatGPT或其他AI工具</li>
                <li>使用下方的分析提示词进行分析</li>
                <li>将AI返回的JSON数据粘贴到下方文本框中</li>
                <li>点击提交继续处理</li>
            </ol>
        </div>

        <!-- 分析提示词 -->
        <div class="mb-3">
            <label class="form-label">
                <i class="fas fa-robot"></i> AI分析提示词
                <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyPrompt()">
                    <i class="fas fa-copy"></i> 复制提示词
                </button>
            </label>
            <textarea class="form-control" id="analysis-prompt" rows="8" readonly></textarea>
        </div>

        <!-- JSON数据输入 -->
        <div class="mb-3">
            <label for="json-data" class="form-label">
                <i class="fas fa-code"></i> 分析结果JSON数据
            </label>
            <textarea class="form-control" id="json-data" rows="15"
                      placeholder="请将AI分析返回的JSON数据粘贴到这里..."></textarea>
            <div class="form-text">请确保JSON格式正确，包含题组信息、图片分析等完整数据</div>
        </div>

        <!-- 提交按钮 -->
        <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-outline-secondary" onclick="validateJson()">
                <i class="fas fa-check"></i> 验证JSON格式
            </button>
            <button type="button" class="btn btn-success" onclick="submitAnalysisData()">
                <i class="fas fa-paper-plane"></i> 提交分析数据
            </button>
        </div>

        <!-- 验证结果 -->
        <div id="json-validation-result" class="mt-3" style="display: none;"></div>
    </div>
</div>

<!-- 处理日志 -->
<div class="card">
    <div class="card-header">
        <i class="fas fa-terminal"></i> 处理日志
        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearProcessingLogs()">
            <i class="fas fa-trash"></i> 清空
        </button>
    </div>
    <div class="card-body">
        <div id="processing-log-container" class="log-container">
            <div class="log-entry log-info">
                <span class="log-timestamp">[系统启动]</span>
                预处理模块已就绪，等待操作...
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let processingInProgress = false;

function uploadFiles() {
    const fileInput = document.getElementById('docxFile');
    const files = fileInput.files;
    const overwriteCheckbox = document.getElementById('overwriteFiles');

    if (files.length === 0) {
        alert('请选择要上传的文件');
        return;
    }

    addProcessingLog('开始上传文件...', 'info');

    const formData = new FormData();
    let validFileCount = 0;

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (!file.name.endsWith('.docx')) {
            addProcessingLog(`跳过非.docx文件: ${file.name}`, 'warning');
            continue;
        }
        formData.append('files', file);
        addProcessingLog(`准备上传: ${file.name}`, 'info');
        validFileCount++;
    }

    if (validFileCount === 0) {
        addProcessingLog('没有有效的.docx文件可上传', 'warning');
        return;
    }

    // 添加覆盖选项
    formData.append('overwrite', overwriteCheckbox.checked ? 'true' : 'false');

    fetch('/api/preprocessor/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addProcessingLog(data.message, 'info');
            fileInput.value = '';
            setTimeout(refreshFileList, 1000);
        } else {
            addProcessingLog(`上传结果: ${data.message}`, 'warning');
        }
    })
    .catch(error => {
        addProcessingLog(`上传请求失败: ${error}`, 'error');
    });
}

function startProcessing() {
    if (processingInProgress) {
        alert('处理正在进行中，请等待完成');
        return;
    }
    
    processingInProgress = true;
    document.getElementById('progress-card').style.display = 'block';
    
    addProcessingLog('开始文档预处理...', 'info');
    updateProgress(0, '初始化处理环境...');
    
    // 发送处理请求到后端
    fetch('/api/preprocessor/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.status === 'waiting_for_cache_choice') {
                addProcessingLog('发现缓存文件，等待用户选择...', 'info');
                // 保持处理状态，不隐藏进度卡片
            } else if (data.status === 'waiting_for_analysis') {
                addProcessingLog('等待手动分析数据提交...', 'warning');
                // 保持处理状态，不隐藏进度卡片
            } else {
                addProcessingLog('处理任务已启动', 'info');
            }
        } else {
            addProcessingLog(`启动失败: ${data.message}`, 'error');
            processingInProgress = false;
            document.getElementById('progress-card').style.display = 'none';
        }
    })
    .catch(error => {
        addProcessingLog(`请求失败: ${error}`, 'error');
        processingInProgress = false;
        document.getElementById('progress-card').style.display = 'none';
    });
}

function updateProgress(percentage, task) {
    console.log('[DEBUG] 更新进度:', percentage + '%', task);  // 添加调试信息

    const progressBar = document.getElementById('progress-bar');
    const currentTask = document.getElementById('current-task');
    const progressCard = document.getElementById('progress-card');

    // 检查DOM元素是否存在
    if (!progressBar) {
        console.error('[ERROR] 找不到进度条元素 #progress-bar');
        return;
    }
    if (!currentTask) {
        console.error('[ERROR] 找不到任务显示元素 #current-task');
        return;
    }
    if (!progressCard) {
        console.error('[ERROR] 找不到进度卡片元素 #progress-card');
        return;
    }

    // 确保进度卡片可见
    if (progressCard.style.display === 'none') {
        progressCard.style.display = 'block';
        console.log('[DEBUG] 显示进度卡片');
    }

    // 更新进度条
    progressBar.style.width = percentage + '%';
    progressBar.textContent = Math.round(percentage) + '%';
    currentTask.textContent = task;

    console.log('[DEBUG] 进度条已更新:', {
        width: progressBar.style.width,
        text: progressBar.textContent,
        task: currentTask.textContent
    });

    if (percentage >= 100) {
        progressBar.classList.remove('progress-bar-animated');
        processingInProgress = false;
        setTimeout(() => {
            progressCard.style.display = 'none';
            console.log('[DEBUG] 隐藏进度卡片');
        }, 3000);
    }
}

function refreshFileList() {
    // 刷新文件列表
    location.reload();
}

function removeFile(filename) {
    if (confirm(`确定要移除文件 "${filename}" 吗？`)) {
        // 发送移除请求到后端
        fetch('/api/preprocessor/remove-file', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({filename: filename})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addProcessingLog(`已移除文件: ${filename}`, 'info');
                refreshFileList();
            } else {
                addProcessingLog(`移除失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            addProcessingLog(`请求失败: ${error}`, 'error');
        });
    }
}

function addProcessingLog(message, level) {
    const logContainer = document.getElementById('processing-log-container');
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry log-${level}`;
    logEntry.innerHTML = `<span class="log-timestamp">[${new Date().toLocaleString()}]</span>${message}`;
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

function clearProcessingLogs() {
    const logContainer = document.getElementById('processing-log-container');
    logContainer.innerHTML = '<div class="log-entry log-info"><span class="log-timestamp">[' + 
        new Date().toLocaleString() + ']</span>日志已清空</div>';
}

// 监听Socket.IO事件
socket.on('preprocessor_progress', function(data) {
    console.log('[DEBUG] 收到进度更新事件:', data);  // 添加调试信息

    // 验证数据格式
    if (typeof data.percentage !== 'number' || typeof data.task !== 'string') {
        console.error('[ERROR] 进度数据格式错误:', data);
        return;
    }

    updateProgress(data.percentage, data.task);
});

socket.on('preprocessor_log', function(data) {
    addProcessingLog(data.message, data.level);
});

socket.on('preprocessor_complete', function(data) {
    addProcessingLog('文档预处理完成！', 'info');
    updateProgress(100, '处理完成');
    refreshFileList();
    refreshDownloadList(); // 刷新下载列表
});

// 监听显示缓存选择界面的事件
socket.on('show_cache_choice', function(data) {
    console.log('收到show_cache_choice事件:', data);
    addProcessingLog('发现缓存文件，请选择处理方式', 'info');
    showCacheChoice(data.document_name, data.cache_summary);
});

// 监听显示JSON提交界面的事件
socket.on('show_json_submit', function(data) {
    console.log('收到show_json_submit事件:', data);
    addProcessingLog('需要手动分析文档结构，请提交分析数据', 'warning');
    addProcessingLog('收到show_json_submit事件，准备显示JSON提交界面', 'info');

    // 更新进度显示为等待手动分析
    updateProgress(50, '等待手动分析文档结构...');

    showJsonSubmit(data.document_name, data.prompt);
});

// JSON提交相关功能
let currentDocumentName = '';
let analysisPromptText = '';

// 缓存选择相关功能
function showCacheChoice(documentName, cacheSummary) {
    currentDocumentName = documentName;

    // 显示缓存摘要
    const summaryDiv = document.getElementById('cache-summary');
    let summaryHtml = '<div class="row">';

    if (cacheSummary.题组数量) {
        summaryHtml += `<div class="col-md-6"><strong>题组数量:</strong> ${cacheSummary.题组数量}</div>`;
    }
    if (cacheSummary.总题目数) {
        summaryHtml += `<div class="col-md-6"><strong>总题目数:</strong> ${cacheSummary.总题目数}</div>`;
    }
    if (cacheSummary.题组范围) {
        summaryHtml += `<div class="col-md-6"><strong>题号范围:</strong> ${cacheSummary.题组范围}</div>`;
    }
    if (cacheSummary.题型统计) {
        const 题型 = Object.entries(cacheSummary.题型统计).map(([type, count]) => `${type}: ${count}`).join(', ');
        summaryHtml += `<div class="col-md-6"><strong>题型分布:</strong> ${题型}</div>`;
    }

    summaryHtml += '</div>';
    summaryDiv.innerHTML = summaryHtml;

    document.getElementById('cache-choice-card').style.display = 'block';

    // 滚动到缓存选择卡片
    document.getElementById('cache-choice-card').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

function hideCacheChoice() {
    document.getElementById('cache-choice-card').style.display = 'none';
    currentDocumentName = null;
    processingInProgress = false;
    document.getElementById('progress-card').style.display = 'none';
}

function handleCacheChoice(choice) {
    if (!currentDocumentName) {
        addProcessingLog('错误：未找到文档名称', 'error');
        return;
    }

    addProcessingLog(`用户选择: ${choice}`, 'info');

    fetch('/api/preprocessor/cache-choice', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            document_name: currentDocumentName,
            choice: choice
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addProcessingLog(data.message, 'info');

            if (choice === 'use_cache') {
                // 使用缓存，隐藏选择界面
                hideCacheChoice();
            } else if (choice === 'reanalyze') {
                // 重新分析，隐藏选择界面，等待JSON提交界面
                hideCacheChoice();
            } else if (choice === 'edit_cache') {
                // 编辑缓存，显示JSON提交界面并预填充数据
                hideCacheChoice();
                if (data.cache_data) {
                    showJsonSubmitWithData(currentDocumentName, data.cache_data);
                }
            }
        } else {
            addProcessingLog(`处理失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addProcessingLog(`请求失败: ${error.message}`, 'error');
    });
}

function showJsonSubmit(documentName, prompt) {
    currentDocumentName = documentName;
    analysisPromptText = prompt;

    document.getElementById('analysis-prompt').value = prompt;
    document.getElementById('json-submit-card').style.display = 'block';

    // 滚动到JSON提交区域
    document.getElementById('json-submit-card').scrollIntoView({ behavior: 'smooth' });
}

function showJsonSubmitWithData(documentName, cacheData) {
    currentDocumentName = documentName;
    analysisPromptText = '编辑现有缓存数据：';

    document.getElementById('analysis-prompt').value = '编辑现有缓存数据：';
    document.getElementById('json-data').value = JSON.stringify(cacheData, null, 2);
    document.getElementById('json-submit-card').style.display = 'block';

    // 滚动到JSON提交区域
    document.getElementById('json-submit-card').scrollIntoView({ behavior: 'smooth' });
}

function hideJsonSubmit() {
    document.getElementById('json-submit-card').style.display = 'none';
    document.getElementById('json-data').value = '';
    document.getElementById('json-validation-result').style.display = 'none';
}

function copyPrompt() {
    const promptTextarea = document.getElementById('analysis-prompt');
    promptTextarea.select();
    document.execCommand('copy');

    // 显示复制成功提示
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i> 已复制';
    button.classList.remove('btn-outline-primary');
    button.classList.add('btn-success');

    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-primary');
    }, 2000);
}

function validateJson() {
    const jsonData = document.getElementById('json-data').value.trim();
    const resultDiv = document.getElementById('json-validation-result');

    if (!jsonData) {
        resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> 请输入JSON数据</div>';
        resultDiv.style.display = 'block';
        return false;
    }

    try {
        const parsed = JSON.parse(jsonData);

        // 验证必要字段
        const requiredFields = ['题组信息', '图片分析'];
        const missingFields = requiredFields.filter(field => !parsed.hasOwnProperty(field));

        if (missingFields.length > 0) {
            resultDiv.innerHTML = `<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> 缺少必要字段: ${missingFields.join(', ')}</div>`;
            resultDiv.style.display = 'block';
            return false;
        }

        resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check"></i> JSON格式验证通过</div>';
        resultDiv.style.display = 'block';
        return true;
    } catch (error) {
        resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times"></i> JSON格式错误: ${error.message}</div>`;
        resultDiv.style.display = 'block';
        return false;
    }
}

function submitAnalysisData() {
    if (!validateJson()) {
        return;
    }

    const jsonData = document.getElementById('json-data').value.trim();

    // 发送JSON数据到后端
    fetch('/api/preprocessor/submit-analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            document_name: currentDocumentName,
            analysis_data: JSON.parse(jsonData)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addProcessingLog('分析数据提交成功，继续处理...', 'info');
            hideJsonSubmit();

            // 继续处理流程
            continueProcessing();
        } else {
            addProcessingLog(`分析数据提交失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addProcessingLog(`提交过程中发生错误: ${error}`, 'error');
    });
}

function continueProcessing() {
    // 继续执行文档处理流程
    addProcessingLog('开始使用提交的分析数据处理文档...', 'info');
    updateProgress(60, '使用分析数据处理文档...');

    fetch('/api/preprocessor/continue-processing', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            document_name: currentDocumentName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addProcessingLog('文档处理完成', 'info');
            updateProgress(100, '处理完成');

            // 重置处理状态
            processingInProgress = false;
            setTimeout(() => {
                document.getElementById('progress-card').style.display = 'none';
            }, 3000);
        } else {
            addProcessingLog(`处理失败: ${data.message}`, 'error');
            processingInProgress = false;
            document.getElementById('progress-card').style.display = 'none';
        }
    })
    .catch(error => {
        addProcessingLog(`处理过程中发生错误: ${error}`, 'error');
        processingInProgress = false;
        document.getElementById('progress-card').style.display = 'none';
    });
}

// 下载功能相关
function refreshDownloadList() {
    const container = document.getElementById('download-list-container');

    // 显示加载状态
    container.innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
            <p>正在加载下载列表...</p>
        </div>
    `;

    fetch('/api/preprocessor/download-list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDownloadList(data.downloads);
            } else {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        加载下载列表失败: ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    加载下载列表时发生错误: ${error.message}
                </div>
            `;
        });
}

function displayDownloadList(downloads) {
    const container = document.getElementById('download-list-container');

    if (downloads.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-folder-open fa-3x mb-3"></i>
                <p>暂无可下载的分析结果</p>
                <small class="text-muted">完成文档处理后，结果将在此显示</small>
            </div>
        `;
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>文档名称</th>
                        <th>文件数量</th>
                        <th>总大小</th>
                        <th>处理时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;

    downloads.forEach(download => {
        const sizeInMB = (download.total_size / (1024 * 1024)).toFixed(2);
        html += `
            <tr>
                <td>
                    <i class="fas fa-file-archive text-success"></i>
                    <strong>${download.document_name}</strong>
                </td>
                <td>
                    <span class="badge bg-info">${download.total_files} 个文件</span>
                </td>
                <td>
                    <span class="text-muted">${sizeInMB} MB</span>
                </td>
                <td>
                    <small class="text-muted">${download.modified_time}</small>
                </td>
                <td>
                    <button class="btn btn-sm btn-success" onclick="downloadResult('${download.document_name}')">
                        <i class="fas fa-download"></i> 下载ZIP
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
        <div class="mt-3">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>下载说明：</strong>
                <ul class="mb-0 mt-2">
                    <li><strong>JSON文件</strong>：可直接导入数据库的题库文件</li>
                    <li><strong>图片文件</strong>：从文档中提取的所有图片资源</li>
                    <li><strong>分析数据</strong>：包含完整的文档结构分析信息</li>
                    <li><strong>处理结果</strong>：AI处理后的格式化文本文件</li>
                </ul>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

function downloadResult(documentName) {
    // 创建下载链接
    const downloadUrl = `/api/preprocessor/download/${documentName}`;

    // 显示下载提示
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 准备下载...';
    button.disabled = true;

    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `${documentName}_analysis_results.zip`;
    link.style.display = 'none';
    document.body.appendChild(link);

    // 触发下载
    link.click();

    // 清理
    document.body.removeChild(link);

    // 恢复按钮状态
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 2000);

    // 添加日志
    addProcessingLog(`开始下载: ${documentName}_analysis_results.zip`, 'info');
}

// 页面加载完成后初始化下载列表
document.addEventListener('DOMContentLoaded', function() {
    refreshDownloadList();
});

</script>
{% endblock %}
